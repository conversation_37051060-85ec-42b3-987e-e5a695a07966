<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>zoomfailpercussionvisualizer</title>
    <style>
        /* Grundlegende Stile für den Body und HTML */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            background-color: black;
            overflow: hidden;
            touch-action: manipulation; /* Optimiert für Touch-Eingaben */
            position: relative; /* Ermöglicht absolute Positionierung von Kindern */
        }

        /* Stil für den oben angezeigten Text */
        .top-text {
            position: absolute;
            top: 10px; /* Abstand vom oberen Rand */
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 1.2em;
            font-family: Arial, Helvetica, sans-serif;
            z-index: 2; /* Höher als .screen (z-index: 1) */
            pointer-events: none; /* Ermöglicht das Klicken durch den Text hindurch, falls nötig */
        }

        /* Stil für jede "Screen"-Ebene */
        .screen {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background-color: black;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.5s ease, opacity 0.5s ease;
            z-index: 1;
        }

        /* Stil für den weißen Rahmen */
        .frame {
            border: 10px solid white;
            box-sizing: border-box;
            width: 90%;
            height: 90%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: black;
            position: relative;
        }

        /* Stil für den Button */
        button {
            padding: 15px 30px;
            font-size: 1.2em;
            background-color: white;
            color: black;
            border: none;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        /* Hover- und Active-Effekte für den Button */
        button:hover {
            background-color: #e0e0e0;
        }

        button:active {
            transform: scale(0.95);
        }

        /* Responsive Anpassungen für kleinere Bildschirme */
        @media (max-width: 600px) {
            button {
                padding: 10px 20px;
                font-size: 1em;
            }
            .frame {
                width: 95%;
                height: 95%;
            }
            .top-text {
                font-size: 1em;
                top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="top-text">use mouse and spacebar for percussion fun with awesome visualisation</div>
    <!-- Initiale Screen-Ebene -->
    <div id="screen" class="screen">
        <div class="frame">
            <button class="zoomButton">zoom out</button>
        </div>
    </div>

    <script>
        /**
         * Funktion zum Erstellen einer neuen Screen-Ebene und Einfügen des aktuellen Screens darin.
         */
        function handleZoom() {
            const currentScreen = document.getElementById('screen');

            // Erstellen einer neuen Screen-Ebene
            const newScreen = document.createElement('div');
            newScreen.classList.add('screen');

            // Erstellen des weißen Rahmens
            const newFrame = document.createElement('div');
            newFrame.classList.add('frame');

            // Verschieben des aktuellen Screens in den neuen Rahmen
            newFrame.appendChild(currentScreen);

            // Hinzufügen der neuen Screen-Ebene zum DOM
            newScreen.appendChild(newFrame);
            document.body.appendChild(newScreen);

            // Animieren des Herauszoomens
            newScreen.style.transform = 'scale(0.8)';
            newScreen.style.opacity = '0';

            // Erzwingen eines Reflows für die Transition
            void newScreen.offsetWidth;

            // Zurücksetzen der Transform- und Opazitätswerte für die Animation
            newScreen.style.transform = 'scale(1)';
            newScreen.style.opacity = '1';

            // Nach der Animation: Aktualisieren der IDs und Event Listener
            setTimeout(() => {
                currentScreen.id = ''; // Entfernen der alten ID
                newScreen.id = 'screen'; // Neue Screen-Ebene erhält die ID

                // Erstellen und Hinzufügen eines neuen Buttons
                const newButton = document.createElement('button');
                newButton.classList.add('zoomButton');
                newButton.textContent = 'zoom out';
                newFrame.appendChild(newButton);

                // Hinzufügen des Event Listeners zum neuen Button
                newButton.addEventListener('click', handleZoom);
            }, 500); // Dauer der Transition in Millisekunden
        }

        // Initialen Event Listener zum ersten Button hinzufügen
        document.querySelector('.zoomButton').addEventListener('click', handleZoom);

        /**
         * Funktion zum Behandeln von Tastendrücken.
         * Aktiviert den Zoom-Button, wenn die Leertaste gedrückt wird.
         */
        function handleKeyDown(event) {
            // Überprüfen, ob die gedrückte Taste die Leertaste ist
            if (event.code === 'Space' || event.key === ' ') {
                event.preventDefault(); // Verhindert das Scrollen der Seite bei Leertaste
                const zoomButton = document.querySelector('.zoomButton');
                if (zoomButton) {
                    zoomButton.click();
                }
            }
        }

        // Hinzufügen des globalen Keydown-Eventlisteners
        document.addEventListener('keydown', handleKeyDown);
    </script>
</body>
</html>
