<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>0nefinity Infinite Canvas</title>
  <link rel="stylesheet" href="advanced-styles.css">
</head>
<body>
  <div id="canvas-container">
    <canvas id="infinite-canvas"></canvas>
  </div>
  
  <div id="toolbar">
    <div class="tool-group">
      <button id="pan-tool" class="tool-button active" title="Pan Tool">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M10,9A1,1 0 0,1 11,8A1,1 0 0,1 12,9V13.47L13.21,13.6L18.15,15.79C18.68,16.03 19,16.56 19,17.14V21.5C18.97,22.32 18.32,22.97 17.5,23H11C10.62,23 10.26,22.85 10,22.57L5.1,18.37L5.84,17.6C6.03,17.39 6.3,17.28 6.58,17.28H6.8L10,19V9M11,5A4,4 0 0,1 15,9C15,10.5 14.2,11.77 13,12.46V11.24C13.61,10.69 14,9.89 14,9A3,3 0 0,0 11,6A3,3 0 0,0 8,9C8,9.89 8.39,10.69 9,11.24V12.46C7.8,11.77 7,10.5 7,9A4,4 0 0,1 11,5Z" />
        </svg>
      </button>
      <button id="text-tool" class="tool-button" title="Text Tool">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M18.5,4L19.66,8.35L18.7,8.61C18.25,7.74 17.79,6.87 17.26,6.43C16.73,6 16.11,6 15.5,6H13V16.5C13,17 13,17.5 13.33,17.75C13.67,18 14.33,18 15,18V19H9V18C9.67,18 10.33,18 10.67,17.75C11,17.5 11,17 11,16.5V6H8.5C7.89,6 7.27,6 6.74,6.43C6.21,6.87 5.75,7.74 5.3,8.61L4.34,8.35L5.5,4H18.5Z" />
        </svg>
      </button>
      <button id="circle-tool" class="tool-button" title="Circle Tool">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
        </svg>
      </button>
      <button id="formula-tool" class="tool-button" title="Formula Tool">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M9.5,11.5H7.5V13.5H9.5V11.5M10.5,11.5H12.5V13.5H10.5V11.5M13.5,15.5H15.5V17.5H13.5V15.5M13.5,11.5H15.5V13.5H13.5V11.5M13.5,7.5H15.5V9.5H13.5V7.5M10.5,15.5H12.5V17.5H10.5V15.5M7.5,15.5H9.5V17.5H7.5V15.5M10.5,7.5H12.5V9.5H10.5V7.5M7.5,7.5H9.5V9.5H7.5V7.5Z" />
        </svg>
      </button>
    </div>
    <div class="tool-group">
      <button id="zoom-in" class="tool-button" title="Zoom In">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M15.5,14L20.5,19L19,20.5L14,15.5V14.71L13.73,14.43C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.43,13.73L14.71,14H15.5M9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14M12,10H10V12H9V10H7V9H9V7H10V9H12V10Z" />
        </svg>
      </button>
      <button id="zoom-out" class="tool-button" title="Zoom Out">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M15.5,14H14.71L14.43,13.73C15.41,12.59 16,11.11 16,9.5A6.5,6.5 0 0,0 9.5,3A6.5,6.5 0 0,0 3,9.5A6.5,6.5 0 0,0 9.5,16C11.11,16 12.59,15.41 13.73,14.43L14,14.71V15.5L19,20.5L20.5,19L15.5,14M9.5,14C7,14 5,12 5,9.5C5,7 7,5 9.5,5C12,5 14,7 14,9.5C14,12 12,14 9.5,14M7,9H12V10H7V9Z" />
        </svg>
      </button>
      <button id="zoom-reset" class="tool-button" title="Reset View">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M12,3A9,9 0 0,0 3,12H0L4,16L8,12H5A7,7 0 0,1 12,5A7,7 0 0,1 19,12A7,7 0 0,1 12,19C10.5,19 9.09,18.5 7.94,17.7L6.5,19.14C8.04,20.3 9.94,21 12,21A9,9 0 0,0 21,12A9,9 0 0,0 12,3Z" />
        </svg>
      </button>
    </div>
    <div class="tool-group">
      <button id="toggle-grid" class="tool-button" title="Toggle Grid">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M10,4V8H14V4H10M16,4V8H20V4H16M16,10V14H20V10H16M16,16V20H20V16H16M14,20V16H10V20H14M8,20V16H4V20H8M8,14V10H4V14H8M8,8V4H4V8H8M10,14H14V10H10V14M4,2H20A2,2 0 0,1 22,4V20A2,2 0 0,1 20,22H4C2.92,22 2,21.1 2,20V4A2,2 0 0,1 4,2Z" />
        </svg>
      </button>
      <button id="toggle-theme" class="tool-button" title="Toggle Theme">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M7.5,2C5.71,3.15 4.5,5.18 4.5,7.5C4.5,9.82 5.71,11.85 7.53,13C4.46,13 2,10.54 2,7.5A5.5,5.5 0 0,1 7.5,2M19.07,3.5L20.5,4.93L4.93,20.5L3.5,19.07L19.07,3.5M12.89,5.93L11.41,5L9.97,6L10.39,4.3L9,3.24L10.75,3.12L11.33,1.47L12,3.1L13.73,3.13L12.38,4.26L12.89,5.93M9.59,9.54L8.43,8.81L7.31,9.59L7.65,8.27L6.56,7.44L7.92,7.35L8.37,6.06L8.88,7.33L10.24,7.36L9.19,8.23L9.59,9.54M19,13.5A5.5,5.5 0 0,1 13.5,19C12.28,19 11.15,18.6 10.24,17.93L17.93,10.24C18.6,11.15 19,12.28 19,13.5M14.6,20.08L17.37,18.93L17.13,22.28L14.6,20.08M18.93,17.38L20.08,14.61L22.28,17.15L18.93,17.38M20.08,12.42L18.94,9.64L22.28,9.88L20.08,12.42M9.63,18.93L12.4,20.08L9.87,22.27L9.63,18.93Z" />
        </svg>
      </button>
    </div>
  </div>
  
  <div id="properties-panel" class="hidden">
    <div class="panel-header">
      <h3>Text Properties</h3>
      <button id="close-properties" class="close-button">×</button>
    </div>
    <div class="property">
      <label for="font-family">Font:</label>
      <select id="font-family">
        <option value="Verdana">Verdana</option>
        <option value="Arial">Arial</option>
        <option value="Times New Roman">Times New Roman</option>
        <option value="Courier New">Courier New</option>
        <option value="Georgia">Georgia</option>
        <option value="Tahoma">Tahoma</option>
        <option value="Trebuchet MS">Trebuchet MS</option>
      </select>
    </div>
    <div class="property">
      <label for="font-size">Size:</label>
      <input type="range" id="font-size" min="8" max="72" value="16">
      <span id="font-size-value">16px</span>
    </div>
    <div class="property">
      <label for="font-color">Color:</label>
      <input type="color" id="font-color" value="#ffffff">
    </div>
    <div class="property">
      <label for="text-align">Alignment:</label>
      <div class="button-group">
        <button id="align-left" class="align-button active">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z" />
          </svg>
        </button>
        <button id="align-center" class="align-button">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M3,3H21V5H3V3M7,7H17V9H7V7M3,11H21V13H3V11M7,15H17V17H7V15M3,19H21V21H3V19Z" />
          </svg>
        </button>
        <button id="align-right" class="align-button">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M3,3H21V5H3V3M9,7H21V9H9V7M3,11H21V13H3V11M9,15H21V17H9V15M3,19H21V21H3V19Z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <div id="circle-properties" class="hidden">
    <div class="panel-header">
      <h3>Circle Properties</h3>
      <button id="close-circle-properties" class="close-button">×</button>
    </div>
    <div class="property">
      <label for="circle-radius">Radius:</label>
      <input type="range" id="circle-radius" min="10" max="300" value="50">
      <span id="radius-value">50px</span>
    </div>
    <div class="property">
      <label for="circle-color">Color:</label>
      <input type="color" id="circle-color" value="#ffffff">
    </div>
    <div class="property">
      <label for="circle-border">Border:</label>
      <input type="range" id="circle-border" min="0" max="20" value="0">
      <span id="border-value">0px</span>
    </div>
    <div class="property">
      <label for="circle-border-color">Border Color:</label>
      <input type="color" id="circle-border-color" value="#ffffff">
    </div>
  </div>
  
  <div id="formula-properties" class="hidden">
    <div class="panel-header">
      <h3>Formula Properties</h3>
      <button id="close-formula-properties" class="close-button">×</button>
    </div>
    <div class="property">
      <label for="formula-input">Formula:</label>
      <input type="text" id="formula-input" value="0 ≡ 1 ≡ ∞">
    </div>
    <div class="property">
      <label for="formula-size">Size:</label>
      <input type="range" id="formula-size" min="10" max="100" value="24">
      <span id="formula-size-value">24px</span>
    </div>
    <div class="property">
      <label for="formula-color">Color:</label>
      <input type="color" id="formula-color" value="#ffffff">
    </div>
  </div>
  
  <div id="minimap-container">
    <canvas id="minimap"></canvas>
  </div>
  
  <div id="info-panel">
    <div class="coordinates">
      <span id="mouse-coordinates">X: 0, Y: 0</span>
      <span id="zoom-level">Zoom: 100%</span>
    </div>
  </div>

  <script src="advanced-canvas.js"></script>
</body>
</html>
