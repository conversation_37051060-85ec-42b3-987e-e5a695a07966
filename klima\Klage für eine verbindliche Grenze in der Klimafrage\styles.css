/* Reset und Basis-Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #000000;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Container */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
header {
    background-color: #000000;
    border-bottom: 1px solid #333333;
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
}

/* Navigation */
.nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #cccccc;
}

/* Main Content */
.main {
    flex: 1;
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    color: #ffffff;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: #ffffff;
}

/* Buttons */
.btn {
    background-color: #333333;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border-radius: 4px;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    background-color: #555555;
    border-color: #777777;
}

.btn-secondary {
    background-color: transparent;
    border-color: #ffffff;
}

.btn-secondary:hover {
    background-color: #ffffff;
    color: #000000;
}

/* Posts List */
.posts-list {
    display: grid;
    gap: 1.5rem;
}

.post-item {
    border: 1px solid #333333;
    padding: 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.post-item:hover {
    border-color: #555555;
    background-color: #111111;
}

.post-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.post-excerpt {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Post Content */
.post-content {
    max-width: 100%;
}

.post-content h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #333333;
    padding-bottom: 1rem;
}

.post-content h2 {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.post-content p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

/* Footer */
footer {
    background-color: #000000;
    border-top: 1px solid #333333;
    padding: 2rem 0;
    text-align: center;
    color: #cccccc;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .nav-toggle {
        display: block;
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #000000;
        border-top: 1px solid #333333;
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .container {
        padding: 0 0.75rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    .post-content h1 {
        font-size: 2rem;
    }
    
    .btn {
        width: 100%;
        text-align: center;
    }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 700px;
    }
}

/* Desktop Styles */
@media (min-width: 1025px) {
    .container {
        max-width: 800px;
    }
    
    .posts-list {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}
