<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concentric Circles</title>
    <link href="/meta.css" rel = "stylesheet">
    <script src="/meta.js" defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            background-color: black;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        canvas {
            position: absolute;
        }
        #controls {
            position: absolute;
            top: 20px;
            left: 80px;
            color: #f0f0f0;
            z-index: 10;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
        label {
            margin-bottom: 10px;
            display: block;
            margin-bottom: 10px;
        }
        input[type="number"] {
            width: 60px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div id="controls">
        <label>
            Number of Circles: <input type="range" id="circleCount" min="0" max="20000" value="10000">
            <input type="number" id="circleCountNumber" min="0" max="20000" value="10000">
            
        </label>
        <label>
            Distance Between Circles: <input type="range" id="circleSpacing" min="0" max="10000" value="0" step="0.1">
            <input type="number" id="circleSpacingNumber" min="0" max="10000" value="0" step="0.1">
            
        </label>
        <label>
            Line Thickness: <input type="range" id="lineThickness" min="0" max="100" value="1" step="0.01">
            <input type="number" id="lineThicknessNumber" min="0" max="100" value="1" step="0.01">
        </label>
    </div>
    <canvas id="circleCanvas"></canvas>
    <script>
        const canvas = document.getElementById('circleCanvas');
        const ctx = canvas.getContext('2d');
        const circleCountInput = document.getElementById('circleCount');
        const circleCountNumber = document.getElementById('circleCountNumber');
        const lineThicknessInput = document.getElementById('lineThickness');
        const lineThicknessNumber = document.getElementById('lineThicknessNumber');
        const circleCountValue = document.getElementById('circleCountValue');
        const circleSpacingInput = document.getElementById('circleSpacing');
        const circleSpacingNumber = document.getElementById('circleSpacingNumber');
        const circleSpacingValue = document.getElementById('circleSpacingValue');

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            drawCircles();
        }

        function drawCircles() {
            const numberOfCircles = parseInt(circleCountInput.value);
            const spacing = parseFloat(circleSpacingInput.value);

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = parseFloat(lineThicknessInput.value);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            for (let i = 0; i < numberOfCircles; i++) {
                ctx.beginPath();
                ctx.arc(centerX, centerY, spacing * (i + 1), 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function syncInputs(rangeInput, numberInput, valueSpan) {
            rangeInput.addEventListener('input', () => {
                numberInput.value = rangeInput.value;
                
                drawCircles();
            });

            numberInput.addEventListener('input', () => {
                rangeInput.value = numberInput.value;
                
                drawCircles();
            });
        }

        syncInputs(circleCountInput, circleCountNumber, circleCountValue);
        syncInputs(circleSpacingInput, circleSpacingNumber, circleSpacingValue);
        syncInputs(lineThicknessInput, lineThicknessNumber, null);

        window.addEventListener('resize', resizeCanvas);

        // Initial setup
        resizeCanvas();
    </script>
</body>
</html>
