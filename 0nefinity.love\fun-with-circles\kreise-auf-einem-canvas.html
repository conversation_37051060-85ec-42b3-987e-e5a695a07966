<!-- kreise-auf-einem-canvas.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Growing Circles</title>
    
    <link href="/meta.css" rel="stylesheet" /> 
    <!-- meta.css regelt Hintergrund- und Objekt-Farben etc.. Sonstiges CSS wird häufig nicht benötigt, es sei denn es wird doch benötigt -->
    <script src="/meta.js" defer></script>
    <!-- meta.js regelt das Menü und den Zurückbutton und evt. sonstige zukünftige Metafunktionen -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        canvas {
            position: absolute;
        }
        #controls {
            position: absolute;
            top: 20px;
            left: 80px;
            z-index: 10;
        }
        label {
            margin-bottom: 10px;
            display: block;
        }
        input[type="number"] {
            width: 60px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div id="controls">
        <label>
            Number of Circles: <input type="range" id="circleCount" min="1" max="10000" value="1000">
            <input type="number" id="circleCountNumber" min="0" max="10000" value="1000">
        </label>
        <label>
            Radius Growth Factor: <input type="number" id="growthFactor" min="0" max="10" value="1" step="0.1">
        </label>
        <label>
            Initial Radius: <input type="number" id="initialRadius" min="0" max="1000" value="5" step="0.1">
        </label>
    </div>
    <canvas id="circleCanvas"></canvas>
    <script>
        const canvas = document.getElementById('circleCanvas');
        const ctx = canvas.getContext('2d');
        const circleCountInput = document.getElementById('circleCount');
        const circleCountNumber = document.getElementById('circleCountNumber');
        const growthFactorInput = document.getElementById('growthFactor');
        const initialRadiusInput = document.getElementById('initialRadius');

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            drawCircles();
        }

        function drawCircles() {
            const numberOfCircles = parseInt(circleCountInput.value);
            const growthFactor = parseFloat(growthFactorInput.value);
            const initialRadius = parseFloat(initialRadiusInput.value);

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            for (let i = numberOfCircles; i > 0; i--) {
                let currentRadius = initialRadius;
                for (let j = 0; j < i - 1; j++) {
                    currentRadius += initialRadius * growthFactor;
                }
                ctx.beginPath();
                ctx.arc(centerX, centerY, currentRadius, 0, Math.PI * 2);
                ctx.fillStyle = (i === 1 || i % 2 !== 0) ? 'white' : 'black';
                ctx.fill();
                ctx.closePath();
            }
        }

        function syncInputs(rangeInput, numberInput) {
            rangeInput.addEventListener('input', () => {
                numberInput.value = rangeInput.value;
                drawCircles();
            });

            numberInput.addEventListener('input', () => {
                rangeInput.value = numberInput.value;
                drawCircles();
            });
        }

        syncInputs(circleCountInput, circleCountNumber);
        growthFactorInput.addEventListener('input', drawCircles);
        initialRadiusInput.addEventListener('input', drawCircles);

        window.addEventListener('resize', resizeCanvas);

        // Initial setup
        window.onload = resizeCanvas;
    </script>
</body>
</html>
