<!-- choose_nothing.html -->
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>choose nothing</title>
  <link href="/meta.css" rel="stylesheet">
  <script src="/meta.js" defer></script>
  <style>
    body {
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      flex-direction: column;
    }

    .container {
      display: flex;
      justify-content: space-between;
      position: relative;
      width: 90%;
      max-width: 600px;
      height: 100px;
      margin-top: 20px;
      flex-wrap: wrap; /* Ermöglicht das Umbrechen der Quadrate */
      justify-content: center; /* Zentriert die Quadrate */
    }

    .square {
      width: 80px;
      height: 80px;
      margin: 10px;
      cursor: pointer;
      position: relative;
      z-index: 2;
      background-color: transparent;
    }

    .black {
      background-color: black;
    }
    .white {
      background-color: white;
    }
    .grey {
      background-color: grey;
    }
    .transparent-grid {
      background-color: transparent;
    }
    .custom-color {
      background-color: #ff00ff;
    }

    body.transparent-background {
      background-image: conic-gradient(#c6c6c6 0%, #c6c6c6 25%, #f7f7f7 25%, #f7f7f7 50%, #c6c6c6 50%, #c6c6c6 75%, #f7f7f7 75%, #f7f7f7 100%);
      background-size: 30px 30px;
      background-position: 0 0;
      background-attachment: fixed;
    }

    body.color-background {
      background-color: #f0f0f0;
    }

    .intro-text {
      font-size: 1.8rem;
      font-weight: bold;
      margin-bottom: 20px;
      width: 90%;
      text-align: center;
    }

    .color-name {
      font-size: 1.2rem;
      margin-top: 20px;
      font-weight: bold;
    }

    #colorPicker {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      display: none;
    }

    /* Media Query für kleinere Bildschirme */
    @media (max-width: 600px) {
      .intro-text {
        font-size: 1.5rem;
      }

      .color-name {
        font-size: 1rem;
      }

      .square {
        width: 60px;
        height: 60px;
        margin: 8px;
      }

      .container {
        flex-direction: column;
        align-items: center;
      }

      #colorPicker {
        max-width: 100px;
      }
    }

  </style>
</head>
<body>

  <div class="intro-text" id="introText">
    choose your representation of nothing
  </div>

  <div class="container">
    <div class="square white" onclick="changeBackground('white')"></div>
    <div class="square black" onclick="changeBackground('black')"></div>
    <div class="square grey" onclick="changeBackground('grey')"></div>
    <div class="square transparent-grid" onclick="changeBackground('transparent')"></div>
    <div class="square custom-color" onclick="openColorPicker(event)"></div>
  </div>

  <div class="color-name" id="colorName"></div>

  <input type="color" id="colorPicker" onchange="setCustomColor(event)" oninput="updateRealTimeColor(event)">

  <script>
    let currentColor = '#ff00ff'; // Default custom color

    function changeBackground(color) {
      let colorName = '';
      let textColor = 'black'; // Standardtextfarbe ist schwarz

      // Wenn "transparent" ausgewählt wird
      if (color === 'transparent') {
        document.body.classList.add('transparent-background'); // Schachbrettmuster anwenden
        document.body.classList.remove('color-background');  // Hintergrundfarbe entfernen
        colorName = 'transparent';
        document.body.style.backgroundColor = 'transparent'; // Hintergrund explizit auf transparent setzen
      } else {
        document.body.classList.remove('transparent-background'); // Entfernen des Schachbrettmusters
        document.body.classList.add('color-background'); // Feste Hintergrundfarbe anwenden
        document.body.style.backgroundColor = color;

        if (color === 'black') {
          colorName = 'black';
          textColor = 'white'; // Text wird weiß, wenn der Hintergrund schwarz ist
        } else if (color === 'white') {
          colorName = 'white';
        } else if (color === 'grey') {
          colorName = 'neutral grey';
        }
      }

      // Setze die Schriftfarbe basierend auf dem Hintergrund
      document.getElementById('colorName').style.color = textColor;
      document.getElementById('introText').style.color = textColor;

      document.getElementById('colorName').textContent = colorName === 'transparent' ? 'transparent' : `${colorName}`;
    }

    function openColorPicker(event) {
      const colorPicker = document.getElementById('colorPicker');
      // Setze die Farbe des Farbwählers auf die aktuelle Custom-Farbe
      colorPicker.value = currentColor;

      // Entferne das Schachbrettmuster, falls es noch aktiv ist
      document.body.classList.remove('transparent-background');
      document.body.classList.add('color-background'); // Sicherstellen, dass der Hintergrund angepasst werden kann

      // Positioniere das Farbwähler-Tool direkt neben dem angeklickten Quadrat
      const rect = event.target.getBoundingClientRect();
      colorPicker.style.left = `${rect.left + 110}px`;  // Platzierung rechts vom Quadrat
      colorPicker.style.top = `${rect.top}px`; // Ausrichtung oben

      colorPicker.click();
      updateRealTimeColor({ target: { value: currentColor } }); // Sofortige Aktualisierung der Farbe
    }

    function updateRealTimeColor(event) {
      const color = event.target.value;
      // Aktualisiere sofort das Quadrat und den Hintergrund
      document.body.style.backgroundColor = color;
      const customColorSquare = document.querySelector('.custom-color');
      customColorSquare.style.backgroundColor = color;

      document.getElementById('colorName').textContent = `your crazy ${color} color`;

      currentColor = color; // Speichern der neuen Custom-Farbe
    }

    function setCustomColor(event) {
      const color = event.target.value;
      currentColor = color; // Speichern der neuen Custom-Farbe
      changeBackground(color);
      document.getElementById('colorName').textContent = `your crazy ${color} color`;

      // Update das Quadrat der Custom-Farbe
      const customColorSquare = document.querySelector('.custom-color');
      customColorSquare.style.backgroundColor = color;
    }
  </script>

</body>
</html>
