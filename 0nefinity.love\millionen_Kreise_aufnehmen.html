<!-- million<PERSON>_<PERSON><PERSON>_aufnehmen.html -->
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Konzentrische Kreise mit Zoom-Aufnahme</title>
    <link href="/meta.css" rel="stylesheet">
    <script src="/meta.js" defer></script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: var(--bg-color); /* Setzt den Hintergrund */
            font-family: Verdana, Geneva, Tahoma, sans-serif;
        }
        canvas {
            display: block;
        }
        #tooltip {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
            pointer-events: none;
            display: none;
            z-index: 1000;
        }
        /* Button-Stile */
        #downloadButton, #recordButton, #stopButton {
            
            right: 70px;
            position: fixed;
            top: 20px;
            padding: 10px 15px;
            background-color: transparent;
            color: var(--text-color);
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1001;
            transition: background-color 0.3s, color 0.3s;
        }
        #downloadButton:hover, #recordButton:hover, #stopButton:hover {
            background-color: var(--text-color);
            color: var(--bg-color);
        }
        #downloadButton {
            right: 210px;
        }
        #recordButton {
            right: 70px;
        }
        #stopButton {
            display: none;
        }
    </style>
</head>
<body>
    <canvas id="circleCanvas"></canvas>
    <div id="tooltip"></div>
    <button id="downloadButton">Bild herunterladen</button>
    <button id="recordButton">Aufnahme starten</button>
    <button id="stopButton">Aufnahme stoppen</button>
    <script>
        // Funktion zum Abrufen der CSS-Variable
        function getCSSVariable(variable) {
            const value = getComputedStyle(document.body).getPropertyValue(variable).trim();
            // console.log(`CSS Variable ${variable}: ${value}`); // Debugging
            return value;
        }

        const canvas = document.getElementById('circleCanvas');
        const ctx = canvas.getContext('2d');
        const tooltip = document.getElementById('tooltip');
        const downloadButton = document.getElementById('downloadButton');
        const recordButton = document.getElementById('recordButton');
        const stopButton = document.getElementById('stopButton');
        let circles = [];
        let zoomLevel = 1;
        let offsetX = 0;
        let offsetY = 0;

        // Aufnahmevariablen vorab definieren
        let isRecording = false;
        let mediaRecorder;
        let recordedChunks = [];
        let startZoomLevel;
        let endZoomLevel;
        let animationStartTime;
        const zoomDuration = 10 * 1000; // 10 Sekunden

        function resizeCanvas() {
            if (!isRecording) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                drawCircles();
            }
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        function drawCircles() {
            ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset Transform
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            circles = [];
            let initialRadius = 1;
            let spacingFactor = 1.01;
            let lineWidth = 0.6;

            const textColor = getCSSVariable('--text-color');
            const bgColor = getCSSVariable('--bg-color');

            // Überprüfe, ob textColor definiert ist
            if (!textColor) {
                console.error("CSS Variable '--text-color' ist nicht definiert.");
                return;
            }

            ctx.strokeStyle = textColor;
            ctx.lineWidth = lineWidth;

            let radius = initialRadius;
            while ((radius * zoomLevel) <= (Math.sqrt(Math.pow(canvas.width, 2) + Math.pow(canvas.height, 2)) / 2 + Math.max(Math.abs(offsetX), Math.abs(offsetY)))) {
                ctx.beginPath();
                ctx.arc(canvas.width / 2 + offsetX, canvas.height / 2 + offsetY, radius * zoomLevel, 0, Math.PI * 2);
                ctx.stroke();
                circles.push({ radius: radius });
                radius *= spacingFactor;
            }
        }

        let hoverTimeout;
        canvas.addEventListener('mousemove', (event) => {
            if (isRecording) return;
            clearTimeout(hoverTimeout);
            tooltip.style.display = 'none';

            const mouseX = (event.clientX - offsetX - canvas.width / 2) / zoomLevel;
            const mouseY = (event.clientY - offsetY - canvas.height / 2) / zoomLevel;
            const distance = Math.sqrt(Math.pow(mouseX, 2) + Math.pow(mouseY, 2));

            for (let i = 0; i < circles.length; i++) {
                if (Math.abs(distance - circles[i].radius) < 5 / zoomLevel) {
                    hoverTimeout = setTimeout(() => {
                        tooltip.style.left = `${event.clientX + 10}px`;
                        tooltip.style.top = `${event.clientY + 10}px`;
                        tooltip.innerText = `Kreis Nummer: ${i + 1}`;
                        tooltip.style.display = 'block';
                    }, 500); // Verkürzte Verzögerung auf 500ms
                    break;
                }
            }
        });

        canvas.addEventListener('mouseout', () => {
            clearTimeout(hoverTimeout);
            tooltip.style.display = 'none';
        });

        canvas.addEventListener('wheel', (event) => {
            if (isRecording) return;
            event.preventDefault();
            const zoomFactor = 1.05;
            if (event.deltaY < 0) {
                zoomLevel *= zoomFactor;
            } else {
                zoomLevel /= zoomFactor;
            }
            drawCircles();
        });

        canvas.addEventListener('mousedown', (event) => {
            if (isRecording) return;
            let startX = event.clientX;
            let startY = event.clientY;
            const startOffsetX = offsetX;
            const startOffsetY = offsetY;

            function onMouseMove(e) {
                offsetX = startOffsetX + (e.clientX - startX);
                offsetY = startOffsetY + (e.clientY - startY);
                drawCircles();
            }

            function onMouseUp() {
                window.removeEventListener('mousemove', onMouseMove);
                window.removeEventListener('mouseup', onMouseUp);
            }

            window.addEventListener('mousemove', onMouseMove);
            window.addEventListener('mouseup', onMouseUp);
        });

        downloadButton.addEventListener('click', () => {
            const width = prompt('Geben Sie die Breite des Bildes in Pixel ein:', window.innerWidth);
            const height = prompt('Geben Sie die Höhe des Bildes in Pixel ein:', window.innerHeight);

            if (width && height) {
                const downloadCanvas = document.createElement('canvas');
                downloadCanvas.width = parseInt(width);
                downloadCanvas.height = parseInt(height);
                const downloadCtx = downloadCanvas.getContext('2d');

                // Hintergrundfarbe setzen
                const bgColor = getCSSVariable('--bg-color') || 'black';
                downloadCtx.fillStyle = bgColor;
                downloadCtx.fillRect(0, 0, downloadCanvas.width, downloadCanvas.height);

                downloadCtx.strokeStyle = getCSSVariable('--text-color') || 'white';
                downloadCtx.lineWidth = 0.6 * (downloadCanvas.width / canvas.width);

                let radius = 1;
                let spacingFactor = 1.01;
                while (radius * zoomLevel < Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2)) / 2) {
                    downloadCtx.beginPath();
                    downloadCtx.arc(
                        downloadCanvas.width / 2 + offsetX * (downloadCanvas.width / canvas.width),
                        downloadCanvas.height / 2 + offsetY * (downloadCanvas.height / canvas.height),
                        radius * zoomLevel * (downloadCanvas.width / canvas.width),
                        0,
                        Math.PI * 2
                    );
                    downloadCtx.stroke();
                    radius *= spacingFactor;
                }

                const link = document.createElement('a');
                link.href = downloadCanvas.toDataURL('image/png');
                link.download = 'konzentrische_kreise.png';
                link.click();
            }
        });

        recordButton.addEventListener('click', () => {
            const width = prompt('Geben Sie die Breite des Videos in Pixel ein:', window.innerWidth);
            const height = prompt('Geben Sie die Höhe des Videos in Pixel ein:', window.innerHeight);
            if (width && height) {
                // Temporär die Canvas-Größe ändern für die Aufnahme
                canvas.width = parseInt(width);
                canvas.height = parseInt(height);
                drawCircles();
            }

            // Event Listener deaktivieren
            window.removeEventListener('resize', resizeCanvas);

            let stream = canvas.captureStream(30); // 30 FPS
            mediaRecorder = new MediaRecorder(stream, { mimeType: 'video/webm; codecs=vp9' });

            mediaRecorder.ondataavailable = function(e) {
                if (e.data.size > 0) {
                    recordedChunks.push(e.data);
                }
            };

            mediaRecorder.onstop = function() {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'zoom_kamerafahrt.webm';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                recordedChunks = [];
                // Event Listener wieder aktivieren
                window.addEventListener('resize', resizeCanvas);
                // Canvas-Größe wiederherstellen
                resizeCanvas();
            };

            isRecording = true;
            recordButton.style.display = 'none';
            stopButton.style.display = 'inline-block';

            startZoomLevel = zoomLevel;
            endZoomLevel = zoomLevel * 10; // 10-facher Zoom

            mediaRecorder.start();
            startZoomAnimation();
        });

        stopButton.addEventListener('click', () => {
            isRecording = false;
            mediaRecorder.stop();
            recordButton.style.display = 'inline-block';
            stopButton.style.display = 'none';
        });

        // Zoom-Animation implementieren
        function startZoomAnimation() {
            animationStartTime = performance.now();
            requestAnimationFrame(animateZoom);
        }

        function animateZoom(time) {
            if (!isRecording) return;
            const elapsed = time - animationStartTime;
            const progress = Math.min(elapsed / zoomDuration, 1);
            zoomLevel = startZoomLevel + (endZoomLevel - startZoomLevel) * progress;
            drawCircles();

            if (progress < 1) {
                requestAnimationFrame(animateZoom);
            } else {
                stopButton.click();
            }
        }
    </script>
</body>
</html>
