point is identical to line

change your perspective, and you will see

(you could also change the perspective of the point/line to see point/line)
((you haveto, if you think it's enough to just render it in 2D like here on screen))
(((try it in your real world rightly in front of you. You will, you can change both at breathtaking speed to see incredible patterns in it)))


Eine Linie kann Länge haben, die ein Punkt nur als Linie haben kann. Aber aus einer bestimmten Perspektive wird die Linie immer ein Punkt bleiben.