ich wollte hier nur die Commit & Push Funktion von VS Code testen

man kann btw statt : im Dateinamen (verboten(!)) diesen Musikalischen Schlawiner hier 𝄈 benutzen. Ist zwar kleiner und hässlicher als der echte, aber zur not kriegen wir so die dateinamenbenennungszeichenzulassungspolicy "ausgetrickst", die die verwendung eines Doppelpunktes unterbindet


alternativ gäbe es auch diese
ː:˸꞉

ː yess
: not valid
˸ yess
꞉ yess

ː˸꞉
ː˸꞉𝄈

und nice, dass man in Dateinamen einfach Lieder mit kuriosen musikalischen Symbolen schreiben kann!