<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>radial mechanics</title>
    <link href="/meta.css" rel="stylesheet">
    <script src="/meta.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        /* Grundlegendes Styling */
        body {
            background: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
            font-family: Verdana, Geneva, Tahoma, sans-serif;
        }

        /* Top Bar Styling */
        .top-bar {
            background: transparent;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap; /* Ermöglicht Zeilenumbruch bei kleineren Bildschirmen */
        }

        .top-bar label {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Aktualisierte Input- und Select-Stile */
        .top-bar input[type="number"],
        .top-bar select {
            width: 60px; /* Für Input-Felder, bei Select-Elementen ggf. anpassen */
            padding: 5px;
            border: 1px solid var(--text-color); /* Setzt den Rahmen auf die Textfarbe */
            border-radius: 4px;
            text-align: right; /* Nur für Input-Felder relevant */
            background: var(--bg-color);
            color: var(--text-color);
            outline: none; /* Entfernt den Standard-Fokusrahmen */
            box-sizing: border-box; /* Stellt sicher, dass Padding und Border in der Breite enthalten sind */
        }

        /* Optional: Hinzufügen eines visuellen Fokus-Indikators */
        .top-bar input[type="number"]:focus,
        .top-bar select:focus {
            border-color: var(--text-color);
            box-shadow: 0 0 5px var(--text-color); /* Optional: fügt einen leichten Schimmer hinzu */
        }

        /* Quadranten Container */
        .quadrants {
            width: 100% !important;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            flex: 1;
            height: calc(100vh - 160px); /* Anpassung für Top Bar */
        }

        /* Gemeinsames Quadrant Styling */
        .quadrant {
            position: relative;
            overflow: hidden;
            background: transparent;
        }

        /* Canvas Styling */
        canvas {
            background: transparent;
            width: 100%;
            height: 100%;
            display: block;
        }

        /* SVG Styling */
        svg {
            background: transparent;
            width: 100%;
            height: 100%;
        }

        /* Three.js Styling */
        #threejs-container canvas {
            display: block;
        }

        /* CSS Quadrant Styling */
        .css-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            --distance: 20%; /* Standardabstand */
            --point-size: 3%; /* Standard Punktgröße */
            transform: scaleY(-1); /* Invertiere die y-Achse */
        }

        .circle-container {
            position: absolute;
            width: 100%;
            height: 100%;
            animation: rotateContainer 5s linear infinite;
            transform-origin: center center;
        }

        .circle {
            position: absolute;
            width: var(--point-size);
            height: var(--point-size);
            background: var(--text-color);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes rotateContainer {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* Label Styling */
        .label {
            position: absolute;
            top: 10px;
            left: 20px;
            font-size: 16px;
            z-index: 10;
        }

        /* CSS-Text Wrapper Styling */
        .css-text-wrapper {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap; /* Verhindert Zeilenumbruch */
        }

        /* CSS-Text Styling */
        .css-text {
            position: relative;
            color: var(--text-color);
            font-size: 16px;
            text-align: center;
            pointer-events: none; /* Verhindert Interaktionen */
            padding: 0 10px; 
        }

        /* Pseudo-Element für den Rand */
        .css-text::before {
            content: '';
            position: absolute;
            top: -5px; /* Anpassung je nach gewünschter Position */
            left: 0;
            width: 110px;
            height: 30px; /* Höhe des Rands */
            border: 2px solid var(--text-color);
            border-radius: 5px;
            pointer-events: none; /* Verhindert Interaktionen */
        }

        button#rotationDirectionButton {
            background: transparent; /* Hintergrund transparent */
            color: var(--text-color);
            padding: 5px 10px; /* Abstand im Button */
            font-size: 16px; /* Schriftgröße */
            cursor: pointer; /* Zeigt einen Zeiger-Cursor an */
            border: 1px solid var(--text-color); /* Rahmen auf Textfarbe setzen */
            border-radius: 4px;
            outline: none;
            box-sizing: border-box;
        }

        /* Optional: Fokus-Stil für den Button */
        button#rotationDirectionButton:focus {
            box-shadow: 0 0 5px var(--text-color);
        }
    </style>
</head>
<body>

    <!-- Top Bar mit Steuerelementen -->
    <div class="top-bar">
        <label>
            Punktgröße:
            <input type="number" id="pointSize" min="0" value="3">
        </label>
        <label>
            <button id="rotationDirectionButton">↻</button>
        </label>
        <label>
            speed (°/frame):
            <input type="number" id="rotationSpeed" min="0" step="0.1" value="1">
        </label>
        <label>
            radius:
            <input type="number" id="distance" min="0" value="20">
        </label>
        <label>
            Anzahl der Punkte:
            <input type="number" id="pointCount" min="0" value="1">
        </label>
    </div>

    <!-- Quadranten Container -->
    <div class="quadrants">
        <!-- Canvas Quadrant -->
        <div class="quadrant" id="canvas-container">
            <canvas id="canvas"></canvas>
            <div class="label">Canvas</div>
        </div>
        
        <!-- SVG Quadrant -->
        <div class="quadrant">
            <svg id="svg" viewBox="-50 -50 100 100" xmlns="http://www.w3.org/2000/svg">
                <g transform="scale(1, -1)">
                    <!-- Dynamische Kreise werden hier hinzugefügt -->
                </g>
            </svg>
            <div class="label">SVG</div>
        </div>
        
        <!-- Three.js Quadrant -->
        <div class="quadrant" id="threejs-container">
            <div class="label">Three.js</div>
        </div>
        
        <!-- CSS Quadrant -->
        <div class="quadrant css-container">
            <div class="circle-container">
                <!-- Dynamische Kreise werden hier hinzugefügt -->
            </div>
            <div class="css-text-wrapper">
                <div class="css-text">css is awesome</div>
            </div>
            <div class="label">CSS</div>
        </div>
    </div>

    <script>
        // Gemeinsame Einstellungen
        const settings = {
            pointSize: 3, // Prozent
            rotationDirection: 1, // 1 für Uhrzeigersinn, -1 für Gegen den Uhrzeigersinn
            rotationSpeed: 1, // Grad pro Frame
            distance: 20, // Prozent
            pointCount: 1 // Anzahl der Punkte
        };
    
        // Top Bar Elemente
        const pointSizeInput = document.getElementById('pointSize');
        const rotationDirectionButton = document.getElementById('rotationDirectionButton');
        const rotationSpeedInput = document.getElementById('rotationSpeed');
        const distanceInput = document.getElementById('distance');
        const pointCountInput = document.getElementById('pointCount');
    
        // Funktion zum Abrufen der CSS-Variable '--text-color'
        function getCSSVariable(variable) {
            const value = getComputedStyle(document.body).getPropertyValue(variable).trim();
            console.log(`CSS Variable ${variable}: ${value}`); // Debugging
            return value;
        }
    
        // Event Listener für Punktgröße
        pointSizeInput.addEventListener('input', (e) => {
            let value = parseFloat(e.target.value);
            if (isNaN(value) || value < 0) value = 0;
            settings.pointSize = value;
            pointSizeInput.value = value; // Aktualisiere das Eingabefeld, falls die Eingabe angepasst wurde
            updateAllQuadrants();
        });
    
        // Event-Listener für den Rotationsrichtungs-Button
        rotationDirectionButton.addEventListener('click', () => {
            // Umdrehen der Rotationsrichtung
            settings.rotationDirection *= -1;
    
            // Aktualisieren des Button-Textes
            rotationDirectionButton.textContent = settings.rotationDirection === 1 ? '↻' : '↺';
    
            // Aktualisieren der Quadranten
            updateAllQuadrants();
        });
    
        // Event Listener für Rotationsgeschwindigkeit
        rotationSpeedInput.addEventListener('input', (e) => {
            let value = parseFloat(e.target.value);
            if (isNaN(value) || value < 0) value = 0;
            settings.rotationSpeed = value;
            rotationSpeedInput.value = value; // Aktualisiere das Eingabefeld, falls die Eingabe angepasst wurde
            updateAllQuadrants();
        });
    
        // Event Listener für Abstand vom Mittelpunkt
        distanceInput.addEventListener('input', (e) => {
            let value = parseFloat(e.target.value);
            if (isNaN(value) || value < 0) value = 0;
            settings.distance = value;
            distanceInput.value = value; // Aktualisiere das Eingabefeld, falls die Eingabe angepasst wurde
            updateAllQuadrants();
        });
    
        // Event Listener für Punktanzahl
        pointCountInput.addEventListener('input', (e) => {
            let value = parseInt(e.target.value);
            if (isNaN(value) || value < 0) value = 0; // Erlaube 0
            settings.pointCount = value;
            pointCountInput.value = value; // Aktualisiere das Eingabefeld, falls die Eingabe angepasst wurde
            updateAllQuadrants();
        });
    
        // ===========================
        // CSS Quadrant Aktualisierung
        // ===========================
        const cssContainer = document.querySelector('.css-container');
        const cssCircleContainer = cssContainer.querySelector('.circle-container');
    
        function updateCSSPoints() {
            cssContainer.style.setProperty('--point-size', `${settings.pointSize}%`);
            cssContainer.style.setProperty('--distance', `${settings.distance}%`);
            
            // Entfernen vorhandener Kreise
            cssCircleContainer.innerHTML = '';
            
            // Erstellen neuer Kreise basierend auf pointCount
            for (let i = 0; i < settings.pointCount; i++) {
                const circle = document.createElement('div');
                circle.classList.add('circle');
                cssCircleContainer.appendChild(circle);
            }
        }
    
        function updateCSSRotation() {
            // Vermeiden von Division durch Null
            const speed = settings.rotationSpeed > 0 ? settings.rotationSpeed : 1;
            const newDuration = 5 / speed; // Anpassung der Dauer basierend auf Geschwindigkeit
            cssCircleContainer.style.animationDuration = `${newDuration}s`;
            cssCircleContainer.style.animationDirection = settings.rotationDirection === 1 ? 'normal' : 'reverse';
        }
    
        function updateCSSAll() {
            updateCSSPoints();
            updateCSSRotation();
        }
    
        // ===========================
        // Canvas Quadrant
        // ===========================
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
    
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            // Invertiere die y-Achse und zentriere den Ursprung
            ctx.setTransform(1, 0, 0, -1, canvas.width / 2, canvas.height / 2);
        }
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    
        let canvasAngle = 0;
    
        function drawCanvas(angle) {
            ctx.clearRect(-canvas.width / 2, -canvas.height / 2, canvas.width, canvas.height);
            const radius = Math.min(canvas.width, canvas.height) * (settings.distance / 100); // Angepasster Radius
            const pointRadius = Math.min(canvas.width, canvas.height) * (settings.pointSize / 150); // Angepasste Punktgröße
            const textColor = getCSSVariable('--text-color'); // Farbe aus CSS-Variable
    
            ctx.fillStyle = textColor; // Setze die Füllfarbe dynamisch
    
            for (let i = 0; i < settings.pointCount; i++) {
                // Berechnung der gleichmäßig verteilten Winkel
                const theta = (settings.rotationDirection * (-angle + i * (360 / settings.pointCount))) * Math.PI / 180;
                const x = radius * Math.cos(theta);
                const y = radius * Math.sin(theta);
                ctx.beginPath();
                ctx.arc(x, y, pointRadius, 0, 2 * Math.PI); // Verwende die angepasste Punktgröße
                ctx.fill();
            }
        }
    
        function animateCanvas() {
            drawCanvas(canvasAngle);
            canvasAngle += settings.rotationSpeed;
            requestAnimationFrame(animateCanvas);
        }
    
        animateCanvas();
    
        function updateCanvasPoints() {
            // Änderungen werden automatisch durch die animateCanvas-Funktion übernommen
        }
    
        // ===========================
        // SVG Quadrant
        // ===========================
        const svg = document.getElementById('svg');
        const svgContainer = svg.querySelector('g');
    
        function updateSVGPoints() {
            // Entfernen vorhandener Kreise
            svgContainer.innerHTML = '';
            
            const textColor = getCSSVariable('--text-color'); // Farbe aus CSS-Variable
    
            // Erstellen neuer Kreise basierend auf pointCount
            for (let i = 0; i < settings.pointCount; i++) {
                const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                circle.setAttribute('r', settings.pointSize / 1.5);
                circle.setAttribute('fill', textColor); // Setze die Füllfarbe dynamisch
                svgContainer.appendChild(circle);
            }
        }
    
        function updateSVG(angle) {
            if (settings.pointCount === 0) return; // Kein Update notwendig bei 0 Punkten
    
            const radius = settings.distance;
            const svgCircles = svgContainer.querySelectorAll('circle');
            svgCircles.forEach((circle, index) => {
                // Berechnung der gleichmäßig verteilten Winkel
                const theta = settings.rotationDirection * (-angle + index * (360 / settings.pointCount)) * Math.PI / 180;
                const x = radius * Math.cos(theta);
                const y = radius * Math.sin(theta);
                circle.setAttribute('cx', x);
                circle.setAttribute('cy', y);
            });
        }
    
        let svgAngle = 0;
    
        function animateSVG() {
            updateSVG(svgAngle);
            svgAngle += settings.rotationSpeed;
            requestAnimationFrame(animateSVG);
        }
    
        animateSVG();
    
        // ===========================
        // Three.js Quadrant
        // ===========================
        const threeContainer = document.getElementById('threejs-container');
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, threeContainer.offsetWidth / threeContainer.offsetHeight, 0.1, 1000);
        camera.position.z = 100;
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(threeContainer.offsetWidth, threeContainer.offsetHeight);
        threeContainer.appendChild(renderer.domElement);
    
        // Anpassung bei Größenänderung
        window.addEventListener('resize', () => {
            camera.aspect = threeContainer.offsetWidth / threeContainer.offsetHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(threeContainer.offsetWidth, threeContainer.offsetHeight);
        });
    
        // Dynamische Punkte erstellen
        let points = [];
        const geometry = new THREE.CircleGeometry(3, 32);
        let material = new THREE.MeshBasicMaterial({ color: 0xffffff }); // Initiale Farbe, wird später aktualisiert
    
        function createThreeJSPoints() {
            // Entfernen vorhandener Punkte aus der Szene
            points.forEach(point => {
                scene.remove(point);
            });
            points = [];
            
            const textColor = getCSSVariable('--text-color'); // Farbe aus CSS-Variable
    
            // Aktualisiere das Material mit der neuen Farbe direkt
            material = new THREE.MeshBasicMaterial({ color: textColor });
    
            // Erstellen neuer Punkte basierend auf pointCount
            for (let i = 0; i < settings.pointCount; i++) {
                const circle = new THREE.Mesh(geometry, material.clone()); // Verwende eine Kopie des Materials
                scene.add(circle);
                points.push(circle);
            }
        }
    
        function updateThreeJSPoints() {
            if (settings.pointCount === 0) return; // Kein Update notwendig bei 0 Punkten
    
            const radius = settings.distance * 1.5; // Skalierung für Three.js
            const textColor = getCSSVariable('--text-color'); // Farbe aus CSS-Variable
    
            // Aktualisiere das Material für alle Punkte
            points.forEach((point, index) => {
                point.material.color.set(textColor);
                
                // Berechnung der gleichmäßig verteilten Winkel
                const theta = (settings.rotationDirection * (-threeAngle + index * (360 / settings.pointCount))) * Math.PI / 180;
                point.position.x = radius * Math.cos(theta);
                point.position.y = radius * Math.sin(theta);
                point.scale.set(settings.pointSize / 3, settings.pointSize / 3, 1); // Anpassung der Punktgröße
            });
        }
    
        let threeAngle = 0;
    
        function animateThreeJS() {
            threeAngle += settings.rotationSpeed;
            updateThreeJSPoints();
            renderer.render(scene, camera);
            requestAnimationFrame(animateThreeJS);
        }
    
        createThreeJSPoints();
        animateThreeJS();
    
        // ===========================
        // Update Funktion für alle Quadranten
        // ===========================
        function updateAllQuadrants() {
            updateCSSAll();
            updateCanvasPoints();
            updateSVGPoints();
            createThreeJSPoints();
            updateThreeJSPoints();
        }
    
        // ===========================
        // Beobachten von Änderungen der CSS-Variable '--text-color'
        // ===========================
        // Funktion zum Aktualisieren der Farbe in allen Quadranten
        function updatePointColors() {
            const textColor = getCSSVariable('--text-color');
            // Canvas wird automatisch aktualisiert durch drawCanvas
            // SVG: Aktualisiere alle Kreise
            const svgCircles = svgContainer.querySelectorAll('circle');
            svgCircles.forEach(circle => {
                circle.setAttribute('fill', textColor);
            });
            // Three.js: Aktualisiere die Farbe der Materialien
            points.forEach(point => {
                point.material.color.set(textColor);
            });
        }
    
        // Beobachte Änderungen am Style-Attribut des Body
        const observer = new MutationObserver(updatePointColors);
        observer.observe(document.body, { attributes: true, attributeFilter: ['style'] });
    
        // Initiale Aktualisierung
        updateAllQuadrants();
    </script>
    
    
</body>
</html>
