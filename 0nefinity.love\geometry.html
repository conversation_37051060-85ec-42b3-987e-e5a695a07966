<!-- geometry.html -->
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese Zeile hier unangetastet lassen) -->
  <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen)-->

  <meta name="description" content="nichtduale Geometrie" />
  <meta name="keywords" content="0nefinity , nondual geometry, 0 ≡ 1 ≡ ∞, 0 = 1 = ∞, nichtduale Mathematik, Wissenschaft, Spiritualität, Philosophie, Unendlichkeit, Nichts, All<PERSON>, Einhe<PERSON>, <PERSON><PERSON> Sein, heilige Mathematik" />

  <title>nondual geometry</title>
  <!-- Ein geometrisches Objekt kann beliebig viele Dimensionen haben
    -- Jede einzelne Dimension besitzt unendlich viel mehr Freiheitsgrade als die vorherige
    -- 
    --
    --
     -->