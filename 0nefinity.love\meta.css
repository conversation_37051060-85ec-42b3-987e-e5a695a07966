/* meta.css */
:root {
    --bg-color: black;
    --text-color: white;
    font-size: 16px; /* Ba<PERSON>größe; wird über Media Query auf mobilen Geräten angepasst */
    background: var(--bg-color);
    color: var(--text-color);
}

body {
    overflow: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-family: Verdana, Geneva, Tahoma, sans-serif;
    line-height: 1.5;
    font-size: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    text-align: center;
    touch-action: manipulation;
    margin: 0;
}


*,
*::before,
*::after {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 50rem; /* 800px / 16 */
    padding-bottom: 7.5rem; /* 120px / 16 */
    white-space: pre-line;
}

section {
    margin: 0.9375rem 0; /* ca. 15px / 16 */
}


h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

p {
    margin: 1.25rem 0; /* ca. 20px / 16 */
}

a {
    color: var(--text-color);
    text-decoration: underline;
}

.menu-button,
.back-button {
    position: fixed;
    top: 0;
    width: 4.375rem; /* ca. 70px */
    height: 4.375rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color);
    font-size: 3.5625rem; /* ca. 57px */
    cursor: pointer;
    z-index: 1000;
    transform-origin: center;
    white-space: nowrap;
}

.menu-button {
    right: 0;
    min-width: 4.375rem;
    max-width: 31.25rem; /* ca. 500px / 16 */
}

.menu-button:hover {
    scale: 1.05;
}

.back-button {
    left: 0;
}

.back-button:hover {
    scale: 1.3;
}

.menu-button.expanded {
    right: 1.25rem; /* ca. 20px / 16 */
    width: 18.75rem; /* ca. 300px / 16 */
}

.menu-button.expanded:hover {
    scale: 1.01;
}

.menu {
    position: fixed;
    top: 5rem; /* ca. 80px / 16 */
    right: 1.25rem; /* ca. 20px / 16 */
    width: 18.75rem; /* ca. 300px / 16 */
    max-height: 90%;
    box-sizing: border-box;
    overflow-y: auto;
    background: var(--bg-color);
    display: none;
    z-index: 1001;
    text-align: left;
}

.menu.open {
    display: block;
}

.menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu ul li {
    margin: 0.625rem 0; /* ca. 10px / 16 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.menu ul li a {
    display: block;
    padding-bottom: 0.3125rem; /* ca. 5px / 16 */
    color: var(--text-color);
    text-decoration: none;
    font-size: 1rem;
    border-bottom: 1px solid transparent;
}

.menu ul li a:hover {
    border-bottom: 1px solid var(--text-color);
}

/* Globale Scrollleisten-Stile für Webkit-basierte Browser */
*::-webkit-scrollbar {
    width: 0.25rem; /* ca. 4px / 16 */
    transition: background 0.3s ease, opacity 0.3s ease;
}

*::-webkit-scrollbar-thumb {
    background: var(--text-color);
    opacity: 0.5;
    border-radius: 0.3125rem; /* ca. 5px / 16 */
    transition: background 0.3s ease, opacity 0.3s ease;
}

*:hover::-webkit-scrollbar-thumb {
    background: var(--text-color);
    opacity: 1;
}

*::-webkit-scrollbar-track {
    background: transparent;
    margin: 0.75rem 0; /* ca. 12px / 16 */
}

.menu::-webkit-scrollbar-thumb {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu:hover::-webkit-scrollbar-thumb {
    opacity: 1;
}


.iframe-container {
    width: 100vw;
    max-width: 400px;
    aspect-ratio: 1;
    margin: 0 auto;
    background: transparent;
  }
  .iframe-container iframe {
    border: none;
    width: 100%;
    height: 100%;
    display: block;
    background: transparent;
  }

/* Media Query für kleinere Geräte */
@media (max-width: 480px) {
    :root {
        font-size: 14px; /* Erhöht die Basis-Schriftgröße, sodass alle Elemente größer wirken */
    }
}
