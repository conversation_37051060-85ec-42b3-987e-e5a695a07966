<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
    <link rel="icon" type="image/png" href="/profilbilder/favicon/index/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/profilbilder/favicon/index/favicon.svg" />
    <link rel="shortcut icon" href="/profilbilder/favicon/index/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/profilbilder/favicon/index/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="0nefinity.love" />
    <link rel="manifest" href="/profilbilder/favicon/index/site.webmanifest" />
    
    <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese Zeile hier unangetastet lassen) -->
    <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen)-->
  
    <title>README</title>
    
    <style>
        body {
            text-align: left;
        }
        h1, h2, h3, h4, h5, h6 {
            text-align: center;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/showdown@2.1.0/dist/showdown.min.js"></script>
</head>
<body>
    <div class="container">
        <div id="content"></div>
    </div>
    <script>
        fetch('README.md')
            .then(response => response.text())
            .then(markdown => {
                const converter = new showdown.Converter({ simpleLineBreaks: true });
                const html = converter.makeHtml(markdown);
                document.getElementById('content').innerHTML = html;
            })
            .catch(error => console.error('Error loading markdown:', error));
    </script>
  <script src="/0nefinity.js" defer></script>
</body>
</html>
