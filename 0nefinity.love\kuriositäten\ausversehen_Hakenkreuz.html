<!-- ausversehen_Hakenkreuz.html -->
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON> und <PERSON>reis aus Einsen</title>

    <!-- 
    muss man echt aufpassen, was man tut. 
    Sobald man manuell die Anzahl der Einsen auf 4 verringert hat, erscheint etwas, was an ein Hakenkreuz erinnert.
    Zu allem Überfluss lässt sich ab genau dem <PERSON>t die Anzahl nicht mehr verändern, weil das UI spakt.
    -->
    
    <style>
        body {
            background-color: black;
            margin: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        svg {
            overflow: visible;
        }

        text {
            fill: white;
        }

        #controls {
            margin-bottom: 20px;
            color: white;
        }

        input[type="number"] {
            width: 80px;
            font-size: 16px;
            margin-left: 10px;
        }
    </style>
</head>
<body>

    <div id="controls">
        <label for="numberInput"><PERSON><PERSON><PERSON> der "1"-Zeichen:</label>
        <input type="number" id="numberInput" value="100" min="1" max="1000" step="1">
    </div>

    <svg width="500" height="400" viewBox="0 0 500 400">
        <defs>
            <!-- Linie -->
            <path id="linie" d="M50 100 L450 100" />

            <!-- Kreis -->
            <path id="kreis" d="
                M250,250
                m-100,0
                a100,100 0 1,0 200,0
                a100,100 0 1,0 -200,0
            " />
        </defs>

        <!-- Text entlang der Linie -->
        <text id="textElementLine" lengthAdjust="spacingAndGlyphs">
            <textPath id="textPathLine" href="#linie">
                <!-- Hier werden die "1"-Zeichen für die Linie eingefügt -->
            </textPath>
        </text>

        <!-- Text entlang des Kreises -->
        <text id="textElementCircle" lengthAdjust="spacingAndGlyphs">
            <textPath id="textPathCircle" href="#kreis">
                <!-- Hier werden die "1"-Zeichen für den Kreis eingefügt -->
            </textPath>
        </text>
    </svg>

    <script>
        // Initial Anzahl der gewünschten "1"-Zeichen einstellen
        let numberOfOnes = parseInt(document.getElementById('numberInput').value);

        // Zugriff auf die Elemente
        const textPathLine = document.getElementById('textPathLine');
        const textElementLine = document.getElementById('textElementLine');
        const pathLine = document.getElementById('linie');

        const textPathCircle = document.getElementById('textPathCircle');
        const textElementCircle = document.getElementById('textElementCircle');
        const pathCircle = document.getElementById('kreis');

        const numberInput = document.getElementById('numberInput');

        // Update-Funktion für die "1"-Zeichen
        function updateOnes() {
            numberOfOnes = parseInt(numberInput.value);

            // Linie aktualisieren
            textPathLine.textContent = '1'.repeat(numberOfOnes);
            const pathLengthLine = pathLine.getTotalLength();
            textElementLine.setAttribute('textLength', pathLengthLine);
            const fontSizeLine = calculateFontSize(pathLengthLine, numberOfOnes);
            textElementLine.setAttribute('font-size', fontSizeLine);

            // Kreis aktualisieren
            textPathCircle.textContent = '1'.repeat(numberOfOnes);
            const pathLengthCircle = pathCircle.getTotalLength();
            textElementCircle.setAttribute('textLength', pathLengthCircle);
            const fontSizeCircle = calculateFontSize(pathLengthCircle, numberOfOnes);
            textElementCircle.setAttribute('font-size', fontSizeCircle);
        }

        // Funktion zur Berechnung der Schriftgröße
        function calculateFontSize(pathLength, numberOfChars) {
            // Durchschnittliche Zeichenbreite (anpassbar je nach Schriftart)
            const averageCharWidth = 0.6; // in em
            // Berechnung der Schriftgröße in Pixeln
            const fontSize = (pathLength / (numberOfChars * averageCharWidth));
            return fontSize;
        }

        // Ereignislistener für das Eingabefeld
        numberInput.addEventListener('input', updateOnes);

        // Initiales Update
        updateOnes();

    </script>

</body>
</html>
