<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>SVG-Vollbildabschnitte mit dynamischem Hintergrund</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow-x: hidden;
    }
    body {
      box-sizing: border-box;
    }
    .section {
      width: 100%;
      height: 100vh; /* Volle Viewport-Höhe */
      display: flex;
      justify-content: center;
      align-items: center;
      /* Standardhintergrund, falls kein SVG-spezifischer gefunden wird */
      background-color: black;
    }
    .section img {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
    }
  </style>
</head>
<body>
    <section class="section">
        <img data-src="es-geht-hier-um-unsere-Grenzen.svg" alt="SVG mit Hintergrund" />
      </section>
      
      <section class="section" data-bg="#333333">
        <img data-src="es-geht-hier-um-unsere-Grenzen-no-bg.svg" alt="SVG ohne Hintergrund" />
      </section>
      

      <script>
        function extractFillColor(element) {
          // Versuche den fill-Wert direkt
          let fillColor = element.getAttribute('fill');
          if (fillColor && fillColor !== 'none') {
            return fillColor;
          }
          // Falls nicht direkt, versuche den style-Attribut zu parsen
          const style = element.getAttribute('style');
          if (style) {
            // Einfacher Parser für "fill:..." im style-String
            const match = style.match(/fill:\s*([^;]+)/);
            if (match && match[1] && match[1] !== 'none') {
              return match[1].trim();
            }
          }
          return null;
        }
      
        function setDynamicBackground(imgElement) {
          const svgUrl = imgElement.dataset.src;
          const section = imgElement.parentElement;
      
          fetch(svgUrl)
            .then(response => response.text())
            .then(svgText => {
              const parser = new DOMParser();
              const svgDoc = parser.parseFromString(svgText, "image/svg+xml");
              
              // Suche nach dem Hintergrund-Rechteck in einem Layer "Hintergrund"
              let bgRect = svgDoc.querySelector('g[inkscape\\:label="Hintergrund"] rect');
              
              let fillColor = null;
              if (bgRect) {
                fillColor = extractFillColor(bgRect);
              }
              
              // Falls keine Farbe gefunden wurde, verwende data-bg
              if (fillColor) {
                section.style.backgroundColor = fillColor;
              } else if (section.dataset.bg) {
                section.style.backgroundColor = section.dataset.bg;
              }
              
              // Setze die Bildquelle, nachdem der Hintergrund angepasst wurde
              imgElement.src = svgUrl;
            })
            .catch(err => {
              console.error("Fehler beim Laden der SVG:", err);
              if (section.dataset.bg) {
                section.style.backgroundColor = section.dataset.bg;
              }
              imgElement.src = svgUrl;
            });
        }
      
        document.querySelectorAll('img[data-src]').forEach(img => {
          setDynamicBackground(img);
        });
      </script>
      
  
</body>
</html>
