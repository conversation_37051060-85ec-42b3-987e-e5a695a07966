<!DOCTYPE html>
<!-- hart komplexes Teil mit beschissener Performance, ich hoffe nicht alle shader sind so -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concentric Circles Shader</title>
    <link href="/meta.css" rel = "stylesheet">
    <script src="/meta.js" defer></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        canvas {
            display: block;
        }
        #controls {
            position: absolute;
            top: 20px;
            left: 80px; 
            z-index: 10;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
        label {
            margin-bottom: 10px;
            display: block;
            margin-bottom: 10px;
        }
        input[type="number"] {
            width: 60px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div id="controls">
        <label>
            Number of Circles: <input type="range" id="circleCount" min="1" max="100" value="20">
            <input type="number" id="circleCountNumber" min="1" max="100" value="20">
        </label>
        <label>
            Distance Between Circles: <input type="range" id="circleSpacing" min="0.01" max="0.1" value="0.05" step="0.01">
            <input type="number" id="circleSpacingNumber" min="0.01" max="0.1" value="0.05" step="0.01">
        </label>
        <label>
            Line Thickness: <input type="range" id="lineThickness" min="0.1" max="5" value="1" step="0.1">
            <input type="number" id="lineThicknessNumber" min="0.1" max="5" value="1" step="0.1">
        </label>
    </div>
    <canvas id="shaderCanvas"></canvas>
    <script>
        const canvas = document.getElementById('shaderCanvas');
        const gl = canvas.getContext('webgl');
        const circleCountInput = document.getElementById('circleCount');
        const circleCountNumber = document.getElementById('circleCountNumber');
        const circleSpacingInput = document.getElementById('circleSpacing');
        const circleSpacingNumber = document.getElementById('circleSpacingNumber');
        const lineThicknessInput = document.getElementById('lineThickness');
        const lineThicknessNumber = document.getElementById('lineThicknessNumber');

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        const vertexShaderSource = `
            attribute vec2 a_position;
            void main() {
                gl_Position = vec4(a_position, 0, 1);
            }
        `;

        let fragmentShaderSource = `
            precision mediump float;
            uniform vec2 u_resolution;
            uniform float u_circleCount;
            uniform float u_circleSpacing;
            uniform float u_lineThickness;
            void main() {
                vec2 uv = gl_FragCoord.xy / u_resolution;
                vec2 center = vec2(0.5, 0.5);
                float aspect = u_resolution.x / u_resolution.y;
                uv.x *= aspect;
                center.x *= aspect;
                float dist = distance(uv, center);
                float intensity = 0.0;
                for (float i = 1.0; i <= 100.0; i++) {
                    if (i > u_circleCount) break;
                    float radius = i * u_circleSpacing;
                    float delta = abs(dist - radius);
                    intensity += smoothstep(0.005, 0.0, delta - u_lineThickness * 0.01);
                }
                gl_FragColor = vec4(vec3(intensity), 1.0);
            }
        `;

        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('Shader compile failed: ', gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            return shader;
        }

        function createProgram(gl, vertexShader, fragmentShader) {
            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);

            if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                console.error('Program link failed: ', gl.getProgramInfoLog(program));
                gl.deleteProgram(program);
                return null;
            }
            return program;
        }

        const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
        const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
        let program = createProgram(gl, vertexShader, fragmentShader);

        gl.useProgram(program);

        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
            -1, -1,
             1, -1,
            -1,  1,
            -1,  1,
             1, -1,
             1,  1,
        ]), gl.STATIC_DRAW);

        const aPositionLocation = gl.getAttribLocation(program, 'a_position');
        gl.enableVertexAttribArray(aPositionLocation);
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.vertexAttribPointer(aPositionLocation, 2, gl.FLOAT, false, 0, 0);

        const uResolutionLocation = gl.getUniformLocation(program, 'u_resolution');
        const uCircleCountLocation = gl.getUniformLocation(program, 'u_circleCount');
        const uCircleSpacingLocation = gl.getUniformLocation(program, 'u_circleSpacing');
        const uLineThicknessLocation = gl.getUniformLocation(program, 'u_lineThickness');

        function render() {
            gl.clear(gl.COLOR_BUFFER_BIT);
            gl.uniform2f(uResolutionLocation, canvas.width, canvas.height);
            gl.uniform1f(uCircleCountLocation, parseFloat(circleCountInput.value));
            gl.uniform1f(uCircleSpacingLocation, parseFloat(circleSpacingInput.value));
            gl.uniform1f(uLineThicknessLocation, parseFloat(lineThicknessInput.value));
            gl.drawArrays(gl.TRIANGLES, 0, 6);
            requestAnimationFrame(render);
        }

        function syncInputs(rangeInput, numberInput) {
            rangeInput.addEventListener('input', () => {
                numberInput.value = rangeInput.value;
                render();
            });

            numberInput.addEventListener('input', () => {
                rangeInput.value = numberInput.value;
                render();
            });
        }

        syncInputs(circleCountInput, circleCountNumber);
        syncInputs(circleSpacingInput, circleSpacingNumber);
        syncInputs(lineThicknessInput, lineThicknessNumber);

        render();
    </script>
</body>
</html>
