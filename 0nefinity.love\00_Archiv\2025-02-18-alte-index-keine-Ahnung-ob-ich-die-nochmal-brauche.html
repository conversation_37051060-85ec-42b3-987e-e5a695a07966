<!-- index.html -->
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <link rel="icon" type="image/png" href="/profilbilder/favicon/index/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/svg+xml" href="/profilbilder/favicon/index/favicon.svg" />
  <link rel="shortcut icon" href="/profilbilder/favicon/index/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/profilbilder/favicon/index/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="0nefinity.love" />
  <link rel="manifest" href="/profilbilder/favicon/index/site.webmanifest" />
  
  <link href="/meta.css" rel="stylesheet" /><!-- meta.css regelt Hintergrund- und Objekt-Farben etc. (Bitte diese Zeile hier unangetastet lassen) -->
  <script src="/meta.js" defer></script><!-- meta.js regelt das Menü und sonstige Funktionen (Bitte diese Zeile hier unangetastet lassen)-->

  <meta name="description" content="0nefinity 0 ≡ 1 ≡ ∞ nichtduale metamathematik, beschreibt die Mathematik und alles, was daraus hervorgeht (Alles)" />
  <meta name="keywords" content="0nefinity, nondual math, nichtduale Mathematik, Wissenschaft, Spiritualität, Philosophie, Unendlichkeit, Nichts, Alles, Einheit, Eins Sein" />

  <title>0nefinity.love</title>

  <style>
    .toggle-text {
      transition: color 0.3s ease;
    }
    .toggle-text:hover {
      color: var(--text-color-hover, #ccc);
    }
    /* Neuer Style für den iframe-Container: kein Rand, kein Hintergrund,
       immer quadratisch, passt sich an den Bildschirm an, aber max. 400x400px */
    .iframe-container {
      width: 100vw;
      max-width: 400px;
      aspect-ratio: 1;
      margin: 0 auto;
      background: transparent;
    }
    .iframe-container iframe {
      border: none;
      width: 100%;
      height: 100%;
      display: block;
      background: transparent;
    }
  </style>
</head>
<body>
  <div class="container">
    0 1 ∞ are basically the same
    they conceptualize different fundamental aspects of this singularity called reality

    look at something (or at nothing)

    realize its Infinity
    realize its <span class="toggle-text" data-original="0neness" data-alternative="0ness">0neness</span>
    realize its Nothingness

    realize the interconnectedness of this aspects

    realize the absolutity in which they arise

    realize everything else as a subset of this
    
    realize 0 1 ∞ swirbeling around, forming existence

    realize how they appear everytime, everywhere, always together

    realize how deep this could go

    realize the eternity of this

    realize what your consciousness is, 
    one infinite emptiness
    where it's possible to instantly create anything you can imagine

    realize thyself

    realize you are 0nefinity

    <h2>Welcome to nondual Mathematics</h2>
    0nefinity describes the metamathematics of mathematics and everything what derives from that

    e.g. nature, physics, religion, spirituality, culture, integrality, reality, you, me, the thing which is us both

    the mathematical I'AMness

    A giant megalomaniacal insolence of Kosmos reducing itself to the simplest possible mathematical formular which above all, describes itself completely

    0 ≡ 1 ≡ ∞

    So simple a child could understand

    Yet so profound that it can never be fully understood

    It can be embraced. You can become it cause you are it.

    Contemplate this, meditate on this, realize its profoundity
    Do some 5-MeO-DMT 

    Realize absolute 0nefinity
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const toggleElements = document.querySelectorAll('.toggle-text');

      toggleElements.forEach(element => {
        const originalText = element.getAttribute('data-original');
        const alternativeText = element.getAttribute('data-alternative');

        element.addEventListener('mouseenter', () => {
          if (alternativeText) {
            element.textContent = alternativeText;
          }
        });

        element.addEventListener('mouseleave', () => {
          if (originalText) {
            element.textContent = originalText;
          }
        });
      });
    });
  </script>
</body>
</html>
