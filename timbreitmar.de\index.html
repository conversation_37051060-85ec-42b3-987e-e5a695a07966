<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title><PERSON> – Projekte</title>
  <meta name="description" content="Private Website von Tim Breitmar" />
  <meta name="theme-color" content="#000000" />
  <meta property="og:title" content="Tim Breitmar" />
  <meta property="og:description" content="Private Website von Tim Breitmar" />
  <meta property="og:type" content="website" />
  <style>
    :root { color-scheme: dark; --bg: #000; --fg: #fff; --muted: rgba(255,255,255,.68); --line: rgba(255,255,255,.12); }
    html, body { height: 100%; }
    * { box-sizing: border-box; }
    body {
      margin: 0; background: var(--bg); color: var(--fg);
      font-family: Verdana, Geneva, Tahoma, sans-serif; line-height: 1.6; letter-spacing: .01em;
    }

    /* Default link look (kept for normal inline links) */
    a { color: var(--fg); text-decoration: none; border-bottom: 1px solid var(--line); }
    a:hover { border-color: var(--fg); }

    .wrap { max-width: 980px; margin: 0 auto; padding: 20px clamp(16px, 4vw, 28px) 80px; }

    /* Header / Nav */
    .topbar {
      position: sticky; top: 0; z-index: 50; backdrop-filter: blur(6px);
      background: linear-gradient(180deg, rgba(0,0,0,.9), rgba(0,0,0,.6));
      border-bottom: 1px solid var(--line);
    }
    .topbar-inner { display: flex; align-items: center; justify-content: space-between; gap: 16px; padding: 12px clamp(16px, 4vw, 28px); max-width: 980px; margin: 0 auto; }
    .brand { font-weight: 700; letter-spacing: .04em; }
    .brand small { display: block; color: var(--muted); font-weight: 400; letter-spacing: .02em; }
    .nav { display: flex; gap: 18px; flex-wrap: wrap; }
    .nav a { border: none; opacity: .9; }
    .nav a:hover { opacity: 1; text-decoration: underline; text-underline-offset: 4px; }

    /* Hero */
    .hero { padding: clamp(36px, 8vw, 72px) 0 24px; border-bottom: 1px solid var(--line); }
    h1 { font-size: clamp(28px, 4.2vw, 46px); margin: 0 0 8px; line-height: 1.2; }
    .lead { color: var(--muted); font-size: clamp(15px, 2.2vw, 18px); max-width: 72ch; }

    /* Sections */
    section { padding: 36px 0; }
    h2 { font-size: clamp(22px, 3.2vw, 32px); margin: 0 0 18px; letter-spacing: .02em; }
    p { margin: 0 0 14px; }

    /* Cards / grids */
    .grid { display: grid; grid-template-columns: 1fr; gap: 16px; }
    @media (min-width: 700px) { .grid { grid-template-columns: 1fr; } }
    @media (min-width: 980px) { .grid { grid-template-columns: 1fr; } }

    .card { border: 1px solid var(--line); border-radius: 16px; padding: 16px; }
    /* remove card hover to avoid whole-card flicker */
    /* .card:hover removed intentionally */
    .card h3 { margin: 0 0 6px; font-size: 18px; letter-spacing: .02em; }
    .card .meta { color: var(--muted); font-size: 12px; margin-bottom: 10px; letter-spacing: .04em; text-transform: uppercase; }

    /* Subproject block-links */
    .sublist { display: grid; gap: 6px; }

    /* The entire subproject becomes the link */
    .subproj {
      display: block;
      padding: 12px 12px;
      border-radius: 12px;
      border: 1px solid transparent; /* reserve space to avoid layout shift */
      border-bottom: none; /* override global link underline */
      background: transparent;
      transition: background-color .18s ease, border-color .18s ease, box-shadow .18s ease, transform .18s ease;
    }
    .subproj:hover { background: rgba(255,255,255,.03); border-color: rgba(255,255,255,.18); box-shadow: 0 4px 18px rgba(0,0,0,.35); }
    .subproj:active { transform: translateY(0); }
    .subproj h3 { margin: 0 0 6px; font-size: 18px; }
    .subproj:hover h3 { text-decoration: underline; text-underline-offset: 3px; }
    .subproj p { color: var(--muted); margin: 0 0 8px; }

    /* Footer */
    footer { padding: 28px 0; border-top: 1px solid var(--line); color: var(--muted); font-size: 14px; }

    /* Nice focus */
    :focus-visible { outline: 2px dashed rgba(255,255,255,.6); outline-offset: 3px; border-radius: 8px; }
    .subproj:focus-visible { outline: 2px dashed rgba(255,255,255,.6); outline-offset: 4px; }

    /* Smooth scroll */
    html { scroll-behavior: smooth; }
  </style>
</head>
<body>
  <header class="topbar" aria-label="Navigation">
    <div class="topbar-inner">
      <div class="brand" aria-label="Brand">
        ⋅
      </div>
      <nav class="nav" aria-label="Abschnitte">
        <a href="#about">Über mich</a>
        <a href="#projects">Projekte</a>
      </nav>
    </div>
  </header>

  <main class="wrap">
    <section id="about" class="hero" aria-labelledby="h-about">
      <h1 id="h-about">Tim Breitmar</h1>
      <br>
      <p class="lead">Als Universum habe ich das Privileg, in einer menschlich scheinenden Form auf der Erde unter den Menschen zu wandeln. </p>
      <p class="lead">Umgeben von augenscheinlich tief gespaltenem Bewusstsein, möchte ich einstehen, für die ultimative Einheit hinter dem Sein.</p>
      <p class="lead">Aus diesem nichtdualen Bewusstsein heraus ergeben sich für mich nicht nur die Entwicklung meines eigenen Bewussteseins und der dazu passenden grundlegendsten mathematischen Theorie (0nefinity), sondern auch ganz praktische irdische Dinge, die meine menschliche Aufmerksamkeit verdienen (pragmatischer Nichtdualismus).</p>
    </section>

    <section id="projects" aria-labelledby="h-projects">
      <h2 id="h-projects">Projekte</h2>

      <div class="grid" role="list">
        <!-- Mathematik / Philosophie -->
        <article class="card" role="listitem">
          <div class="meta">Philosophische/Mathematische Grundlagen</div>

          <div class="sublist">
            <a class="subproj" href="https://0nefinity.love" target="_blank" rel="noopener">
              <h3>0NEFINITY – Die Grundlegendste Mathematische Theorie</h3>
              <p>Die Eine Unendliche Lee(h)re von 0 1 ∞ <br><i>Jetzt mehr erfahren über die tiefere mathematische Natur deines Bewusstseins und den Kern des Universums</i></p>
            </a>

            <a class="subproj" href="https://0nefinity.love/pragmatischer-nichtdualismus.html" target="_blank" rel="noopener">
              <h3>Pragmatischer Nichtdualismus</h3>
              <p>Wer erkennt, er ist das Universum, er ist die Liebe, der erkennt, er ist die Menschheit und er ist hier sie zu lieben.</p>
            </a>
          </div>
        </article>

        <!-- Politische Arbeit -->
        <article class="card" role="listitem">
          <div class="meta">Politische Arbeit</div>

          <div class="sublist">
            <a class="subproj" href="#">
              <h3>EINE MENSCHHEIT</h3>
              <p>Transzendierung des Nationalismus – <i>Du bist mehr Mensch, als deine Nationalität.</i></p>
            </a>

            <a class="subproj" href="https://capfossil.org" target="_blank" rel="noopener">
              <h3>Klima – capfossil.org</h3>
              <p>Menschen schützen, fossile global limitieren.</p>
            </a>

            <a class="subproj" href="#">
              <h3>Nichtdual-Integrale-Partei (NIP)</h3>
              <p>Parteien ehren, Parteien transzendieren.</p>
            </a>
          </div>
        </article>

        <!-- Unternehmerische Arbeit -->
        <article class="card" role="listitem">
          <div class="meta">Unternehmerische Arbeit</div>

          <div class="sublist">
            <a class="subproj" href="pv.html">
              <h3>Photovoltaik</h3>
              <p>Akquise von Großprojekten im Raum NRW<br>Photovoltaikanlagen für Privatkunden im Raum Dortmund</p>
            </a>

            <a class="subproj" href="green-industry.html">
              <h3>Green Industry</h3>
              <p>Defossilisierung von Kunststoffen und Chemie <em>(coming soon)</em></p>
            </a>
          </div>
        </article>

        <!-- Sonstige Projekte -->
        <article class="card" role="listitem">
          <div class="meta">Sonstige Projekte</div>

          <div class="sublist">
            <a class="subproj" href="#">
              <h3>Globales Privates Vermögen</h3>
              <p>Wir sind das Privat!</p>
            </a>
          </div>
        </article>
      </div>
    </section>

    <footer>
      <div class="wrap" style="padding:0;">
        © <span id="y"></span> Tim Breitmar
      </div>
    </footer>
  </main>

  <script>
    // Aktualisiere Jahr im Footer
    document.getElementById('y').textContent = new Date().getFullYear();
  </script>
</body>
</html>
