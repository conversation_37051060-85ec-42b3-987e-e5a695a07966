<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weiße 1 auf schwarzem Hintergrund</title>
    <link href="/meta.css" rel="stylesheet">
    <script src="/meta.js" defer></script>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-size: 10em;
            position: relative;
            flex-direction: column; /* Ermöglicht die Platzierung von zusätzlichen Elementen */
        }
        .number-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .number {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0.1;
        }
    </style>
    <script>
        function createNumbers(count) {
            const container = document.querySelector('.number-container');
            container.innerHTML = ''; // Nur den Zahlen-Container leeren
            for (let i = 0; i < count; i++) {
                const div = document.createElement('div');
                div.className = 'number';
                div.textContent = '1';
                div.style.opacity = (1 / (i + 1)).toString();
                container.appendChild(div);
            }
        }

        window.onload = function() {
            const count = prompt("Wie viele Einsen möchten Sie übereinander legen?");
            const parsedCount = parseInt(count);
            if (!isNaN(parsedCount) && parsedCount >= 0) {
                createNumbers(Math.min(parsedCount, 50));
            } else {
                alert("Bitte eine gültige Zahl eingeben.");
            }
        }
    </script>
</head>
<body>
    <div class="number-container"></div>
</body>
</html>
