#!/usr/bin/env python3
"""
Folder Structure Generator

This script creates a comprehensive JSON representation of the folder and file structure
starting from the directory where the script is located. It recursively traverses all
subdirectories and creates a hierarchical JSON structure that can be used to provide
context to AI agents about project organization.

Features:
- Recursive directory traversal
- File size information
- File extension categorization
- Timestamps (creation and modification)
- Configurable depth limits
- Hidden file filtering options
- Pretty-printed JSON output
"""

import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional


class FolderStructureGenerator:
    def __init__(self, 
                 root_path: str = None,
                 max_depth: int = None,
                 include_hidden: bool = False,
                 include_file_stats: bool = True,
                 exclude_patterns: List[str] = None):
        """
        Initialize the folder structure generator.
        
        Args:
            root_path: Starting directory (defaults to script location)
            max_depth: Maximum recursion depth (None for unlimited)
            include_hidden: Whether to include hidden files/folders
            include_file_stats: Whether to include file size and timestamps
            exclude_patterns: List of patterns to exclude (e.g., ['*.pyc', '__pycache__'])
        """
        self.root_path = Path(root_path) if root_path else Path(__file__).parent
        self.max_depth = max_depth
        self.include_hidden = include_hidden
        self.include_file_stats = include_file_stats
        self.exclude_patterns = exclude_patterns or [
            '__pycache__', '*.pyc', '*.pyo', '.git', '.svn', 
            'node_modules', '.DS_Store', 'Thumbs.db'
        ]
        
    def should_exclude(self, path: Path) -> bool:
        """Check if a path should be excluded based on patterns."""
        if not self.include_hidden and path.name.startswith('.'):
            return True
            
        for pattern in self.exclude_patterns:
            if pattern.startswith('*'):
                if path.name.endswith(pattern[1:]):
                    return True
            elif pattern in str(path):
                return True
                
        return False
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Get detailed information about a file."""
        info = {
            "name": file_path.name,
            "type": "file",
            "extension": file_path.suffix.lower() if file_path.suffix else None
        }
        
        if self.include_file_stats:
            try:
                stat = file_path.stat()
                info.update({
                    "size_bytes": stat.st_size,
                    "size_human": self.format_size(stat.st_size),
                    "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
            except (OSError, PermissionError):
                info["error"] = "Cannot access file stats"
                
        return info
    
    def get_directory_info(self, dir_path: Path, current_depth: int = 0) -> Dict[str, Any]:
        """Get detailed information about a directory and its contents."""
        info = {
            "name": dir_path.name if dir_path != self.root_path else "ROOT",
            "type": "directory",
            "path": str(dir_path.relative_to(self.root_path)),
            "children": []
        }
        
        # Check depth limit
        if self.max_depth is not None and current_depth >= self.max_depth:
            info["note"] = f"Max depth ({self.max_depth}) reached"
            return info
        
        try:
            # Get all items in directory
            items = []
            for item in dir_path.iterdir():
                if self.should_exclude(item):
                    continue
                items.append(item)
            
            # Sort items: directories first, then files, both alphabetically
            items.sort(key=lambda x: (x.is_file(), x.name.lower()))
            
            # Process each item
            for item in items:
                try:
                    if item.is_file():
                        info["children"].append(self.get_file_info(item))
                    elif item.is_dir():
                        info["children"].append(
                            self.get_directory_info(item, current_depth + 1)
                        )
                except (PermissionError, OSError) as e:
                    info["children"].append({
                        "name": item.name,
                        "type": "error",
                        "error": str(e)
                    })
                    
        except (PermissionError, OSError) as e:
            info["error"] = str(e)
            
        return info
    
    def format_size(self, size_bytes: int) -> str:
        """Convert bytes to human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def generate_structure(self) -> Dict[str, Any]:
        """Generate the complete folder structure."""
        structure = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "root_path": str(self.root_path.absolute()),
                "generator_version": "1.0",
                "settings": {
                    "max_depth": self.max_depth,
                    "include_hidden": self.include_hidden,
                    "include_file_stats": self.include_file_stats,
                    "exclude_patterns": self.exclude_patterns
                }
            },
            "structure": self.get_directory_info(self.root_path)
        }
        
        return structure
    
    def save_to_file(self, output_file: str = "folder_structure.json", 
                     indent: int = 2) -> str:
        """Generate and save the folder structure to a JSON file."""
        structure = self.generate_structure()
        
        output_path = self.root_path / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(structure, f, indent=indent, ensure_ascii=False)
            
        return str(output_path)


def main():
    """Main function to run the folder structure generator."""
    print("🗂️  Folder Structure Generator")
    print("=" * 50)
    
    # Create generator with default settings
    generator = FolderStructureGenerator(
        max_depth=10,  # Reasonable default to prevent infinite recursion
        include_hidden=False,
        include_file_stats=True
    )
    
    print(f"📁 Scanning directory: {generator.root_path}")
    print("⏳ Generating structure...")
    
    try:
        # Generate and save structure
        output_file = generator.save_to_file()
        
        # Get some basic stats
        structure = generator.generate_structure()
        
        print(f"✅ Structure saved to: {output_file}")
        print(f"📊 Generated at: {structure['metadata']['generated_at']}")
        print("\n🎯 This JSON can now be used to provide context to AI agents!")
        
        # Show a preview of the structure
        print("\n📋 Preview of generated structure:")
        print(json.dumps(structure['structure'], indent=2)[:500] + "...")
        
    except Exception as e:
        print(f"❌ Error generating structure: {e}")


if __name__ == "__main__":
    main()
