// Blog Application
class BlogApp {
    constructor() {
        this.posts = [];
        this.currentSection = 'home';
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadPosts();
        this.handleRouting();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });

        // Mobile menu toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });

        // Show posts button
        document.getElementById('show-posts-btn').addEventListener('click', () => {
            this.showSection('posts');
        });

        // Back to posts button
        document.getElementById('back-to-posts').addEventListener('click', () => {
            this.showSection('posts');
        });

        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            this.handleRouting();
        });
    }

    async loadPosts() {
        // Liste der bekannten Markdown-Dateien
        const postFiles = [
            'Einer unserer größten Fehler.md',
            'Gefährlichkeiten.md',
            'lasst euch nicht einlullen!.md'
        ];

        this.posts = [];

        for (const filename of postFiles) {
            try {
                const response = await fetch(filename);
                if (response.ok) {
                    const content = await response.text();
                    const post = this.parsePost(content, filename);
                    this.posts.push(post);
                }
            } catch (error) {
                console.warn(`Konnte ${filename} nicht laden:`, error);
            }
        }

        this.renderPostsList();
    }

    parsePost(content, filename) {
        const lines = content.split('\n');
        const title = lines[0] || filename.replace('.md', '');
        
        // Erstelle einen Excerpt aus den ersten paar Zeilen
        let excerpt = '';
        for (let i = 1; i < lines.length && excerpt.length < 150; i++) {
            if (lines[i].trim()) {
                excerpt += lines[i].trim() + ' ';
            }
        }
        
        if (excerpt.length > 150) {
            excerpt = excerpt.substring(0, 150) + '...';
        }

        return {
            title: title,
            content: content,
            excerpt: excerpt,
            filename: filename,
            slug: this.createSlug(title)
        };
    }

    createSlug(title) {
        return title
            .toLowerCase()
            .replace(/[äöüß]/g, (match) => {
                const replacements = { 'ä': 'ae', 'ö': 'oe', 'ü': 'ue', 'ß': 'ss' };
                return replacements[match] || match;
            })
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
    }

    renderPostsList() {
        const postsContainer = document.getElementById('posts-list');
        
        if (this.posts.length === 0) {
            postsContainer.innerHTML = '<p>Keine Blog-Beiträge gefunden.</p>';
            return;
        }

        postsContainer.innerHTML = this.posts.map(post => `
            <div class="post-item" data-slug="${post.slug}">
                <h3 class="post-title">${post.title}</h3>
                <p class="post-excerpt">${post.excerpt}</p>
            </div>
        `).join('');

        // Event Listener für Post-Items
        document.querySelectorAll('.post-item').forEach(item => {
            item.addEventListener('click', () => {
                const slug = item.getAttribute('data-slug');
                this.showPost(slug);
            });
        });
    }

    showPost(slug) {
        const post = this.posts.find(p => p.slug === slug);
        if (!post) return;

        const postContent = document.getElementById('post-content');
        
        // Konvertiere Markdown zu HTML
        const htmlContent = marked.parse(post.content);
        postContent.innerHTML = htmlContent;

        this.showSection('post');
        
        // Update URL
        history.pushState({ section: 'post', slug: slug }, '', `#post/${slug}`);
    }

    showSection(sectionName) {
        // Verstecke alle Sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Zeige gewünschte Section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }

        // Schließe Mobile Menu
        document.querySelector('.nav-menu').classList.remove('active');

        // Update URL für normale Sections
        if (sectionName !== 'post') {
            history.pushState({ section: sectionName }, '', `#${sectionName}`);
        }
    }

    handleRouting() {
        const hash = window.location.hash.substring(1);
        
        if (!hash || hash === 'home') {
            this.showSection('home');
        } else if (hash === 'posts') {
            this.showSection('posts');
        } else if (hash === 'about') {
            this.showSection('about');
        } else if (hash.startsWith('post/')) {
            const slug = hash.substring(5);
            this.showPost(slug);
        } else {
            this.showSection('home');
        }
    }
}

// Initialisiere die App wenn DOM geladen ist
document.addEventListener('DOMContentLoaded', () => {
    new BlogApp();
});
