<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>0nefinity Canvas</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div id="canvas-container">
    <canvas id="infinite-canvas"></canvas>
  </div>

  <div id="toolbar">
    <div class="tool-group">
      <button id="pan-tool" class="tool-button active">Pan</button>
      <button id="edit-tool" class="tool-button">✏️ Edit</button>
    </div>
    <div class="tool-group">
      <button id="text-tool" class="tool-button" disabled>Text</button>
      <button id="shape-tool" class="tool-button" disabled>Shape</button>
    </div>
    <div class="tool-group">
      <button id="zoom-in" class="tool-button">+</button>
      <button id="zoom-out" class="tool-button">-</button>
      <button id="zoom-reset" class="tool-button">Reset</button>
    </div>
  </div>

  <div id="properties-panel" class="hidden">
    <h3>Text Properties</h3>
    <div class="property">
      <label for="font-family">Font:</label>
      <select id="font-family">
        <option value="Verdana">Verdana</option>
        <option value="Arial">Arial</option>
        <option value="Times New Roman">Times New Roman</option>
        <option value="Courier New">Courier New</option>
      </select>
    </div>
    <div class="property">
      <label for="font-size">Size:</label>
      <input type="range" id="font-size" min="8" max="72" value="16">
      <span id="font-size-value">16px</span>
    </div>
    <div class="property">
      <label for="font-color">Color:</label>
      <input type="color" id="font-color" value="#ffffff">
    </div>
  </div>

  <div id="minimap-container">
    <canvas id="minimap"></canvas>
  </div>

  <script src="canvas.js"></script>
</body>
</html>
