<!-- nullgeteiltdurchnull.html -->
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Null durch Null teilen</title>
  <link href="/meta.css" rel = "stylesheet">
  <script src="/meta.js" defer></script>
</head>
<body>
    <div class="container">
        <h2>Lässt sich Null durch Null teilen?</h2>
        Alle in folgender gutefrage.net Diskussion genannten Punkte kommen gemeinsam der Antwort recht nahe        
        <br>
        <a href="https://www.gutefrage.net/frage/kann-man-0-durch-0-teilen">https://www.gutefrage.net/schwarmintelligenz</a>
        
        Der wohl wahreste Ausschnitt der Diskussion:

        <img src="verlinkte_Dinge\0durch0-Gute-Frage.png" alt="Gute Frage Dialog">

        Der scheinbare Widerspruch, das Warum und die Fragezeichen spiegeln die bis in die späte Neuzeit reichende Verwirrung zu dieser eigentlich einfachen Frage


        natürlich ist 0/0 = 0 1 und ∞ 

        Es gibt nichts, was nicht zusätzlich, zuletzt und grundsätzlich 0 1 und ∞ ist.

        

      

        warum sollte das das einzige sein, was 





        Warum dieser Fokus aufs teilen durch 0?

        Ich war es gewohnt, im Mathematikuntericht sinnvolle Antworten zu erhalten

        Mathematik ist sinnvoll und besticht durch eine äußerst klare, präzise und eindeutige Logik

        Bei der Frage, warum man nicht durch 0 teilen könne, 

        scheint aber mehr ein Refelex anzugehen, der schnell von allen üebrnommen zu werden scheint

        "Durch null teilen ist nicht erlaubt"
        "Das Ergebnis ist nicht definiert"

        
    </div>
</body>
</html>