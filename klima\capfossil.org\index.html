<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Kein F<PERSON>rlimit – keine Klimasicherheit</title>

  <link rel="preload" href="capfossil.md" as="fetch">

  <style>
    :root { color-scheme: dark; }
    html, body { height: 100%; }

    .wrap { opacity: 0; transition: opacity .2s ease; }
    html.ready .wrap { opacity: 1; }

    body {
      margin: 0; padding: 34px 18px 120px;
      background: #000; color: #fff;
      font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Noto Sans";
      font-size: 19px;
      line-height: 1.62; letter-spacing: .005em;
    }
    .wrap { max-width: 940px; margin: 0 auto; }

    .md h1 { font-size: clamp(36px, 5.6vw, 60px); line-height: 1.06; margin: 150px 0 60px; font-weight: 900; }
    .md h1 + p { margin-top: 14px; }
    .md h2 { font-size: clamp(25px, 3.3vw, 36px); line-height: 1.16; margin: 34px 0 14px; font-weight: 850; }
    .md h3 { font-size: clamp(21px, 2.7vw, 27px); line-height: 1.2; margin: 24px 0 12px; font-weight: 800; }
    .md p  { margin: 16px 0 19px; }
    .md ul, .md ol { padding-left: 22px; margin: 14px 0 18px; }
    .md li { margin: 6px 0; }
    .md blockquote { margin: 16px 0; padding-left: 16px; border-left: 3px solid #444; color: #ddd; }
    .rule { height: 1px; background: #222; margin: 28px 0; }

    footer { margin-top: 48px; font-size: 0.9em; color: #aaa; }
    footer a { color: #9bd; text-decoration: none; }
    footer a:hover { text-decoration: underline; }

    @media print {
      :root { color-scheme: light; }
      body { background: #fff; color: #000; font-size: 12pt; padding-bottom: 60px; }
      .rule { background: #ccc; }
      footer a { color: #06c; }
      .wrap { opacity: 1 !important; }
    }
  </style>

  <script defer>
    // --- Minimaler Markdown-Renderer ---
function renderMarkdown(md) {
  const lines = md.replace(/\u00A0/g, ' ').split(/\r?\n/);
  const out = [];
  let inOl = false, inUl = false;

  const closeLists = () => { if (inOl) { out.push('</ol>'); inOl = false; } if (inUl) { out.push('</ul>'); inUl = false; } };
  const esc = s => s.replace(/[&<>]/g, c => ({ '&': '&amp;', '<': '&lt;', '>': '&gt;' }[c]));
  const inline = s => {
    let t = esc(s);
    t = t.replace(/\*\*([^*]+)\*\*/g, (_, g1) => '<strong>' + g1 + '</strong>');
    t = t.replace(/\*([^*]+)\*/g,  (_, g1) => '<em>' + g1 + '</em>');
    t = t.replace(/(https?:\/\/[^\s]+)/g, m => '<a href="' + m + '" target="_blank" rel="noopener">' + m + '</a>');
    t = t.replace(/([A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,})/gi, m => '<a href="mailto:' + m + '">' + m + '</a>');
    return t;
  };

  for (const raw of lines) {
    const l = raw;
    if (/^\s*$/.test(l)) { closeLists(); continue; }
    if (/^\s*---+\s*$/.test(l)) { closeLists(); out.push('<div class="rule"></div>'); continue; }
    if (/^######\s+/.test(l)) { closeLists(); out.push('<h6>' + inline(l.replace(/^######\s+/, '')) + '</h6>'); continue; }
    if (/^#####\s+/.test(l))  { closeLists(); out.push('<h5>' + inline(l.replace(/^#####\s+/, '')) + '</h5>'); continue; }
    if (/^####\s+/.test(l))   { closeLists(); out.push('<h4>' + inline(l.replace(/^####\s+/, '')) + '</h4>'); continue; }
    if (/^###\s+/.test(l))    { closeLists(); out.push('<h3>' + inline(l.replace(/^###\s+/, '')) + '</h3>'); continue; }
    if (/^##\s+/.test(l))     { closeLists(); out.push('<h2>' + inline(l.replace(/^##\s+/, '')) + '</h2>'); continue; }
    if (/^#\s+/.test(l))      { closeLists(); out.push('<h1>' + inline(l.replace(/^#\s+/, '')) + '</h1>'); continue; }
    if (/^>\s?/.test(l))      { closeLists(); out.push('<blockquote>' + inline(l.replace(/^>\s?/, '')) + '</blockquote>'); continue; }
    if (/^\d+\.\s+/.test(l)) {
      if (!inOl) { closeLists(); out.push('<ol>'); inOl = true; }
      out.push('<li>' + inline(l.replace(/^\d+\.\s+/, '')) + '</li>'); continue;
    }
    if (/^[-*]\s+/.test(l)) {
      if (!inUl) { closeLists(); out.push('<ul>'); inUl = true; }
      out.push('<li>' + inline(l.replace(/^[-*]\s+/, '')) + '</li>'); continue;
    }
    closeLists(); out.push('<p>' + inline(l) + '</p>');
  }
  closeLists();
  return out.join('\n');
}

// --- Init, robust gegen Hot-Reload & bfcache ---
let IS_RENDERING = false;
async function init() {
  if (IS_RENDERING) return;
  IS_RENDERING = true;

  const el = document.getElementById('content');
  try {
    // Cache-Bust: holt wirklich die aktuelle MD
    const res = await fetch('capfossil.md?ts=' + Date.now(), { cache: 'no-store' });
    if (!res.ok) throw new Error('capfossil.md nicht gefunden');
    const md = await res.text();
    el.innerHTML = renderMarkdown(md);
  } catch (err) {
    console.error('[render] Fehler:', err);
    el.innerHTML = '<p><em>Inhalt konnte nicht geladen werden.</em></p>';
  } finally {
    document.documentElement.classList.add('ready'); // jetzt einblenden
    IS_RENDERING = false;
  }
}

// 1) Beim ersten Laden
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}

// 2) Bei Seitenwiederkehr (bfcache)
window.addEventListener('pageshow', init);

// 3) Wenn Five Server das Body/DOM tauscht (Hot-Reload ohne Reload)
const mo = new MutationObserver(() => {
  const content = document.getElementById('content');
  if (content && content.childNodes.length === 0) {
    init(); // neu rendern
  }
});
mo.observe(document.documentElement, { childList: true, subtree: true });

  </script>
</head>
<body>
  <main class="wrap">
    <div id="content" class="md" aria-live="polite"></div>

    <footer>
      <h2>Impressum</h2>
      <p>Tim Breitmar<br>
      Holterhöfe 3<br>
      47877 Willich<br><br>
      <a href="mailto:<EMAIL>"><EMAIL></a><br>
      <a href="tel:+4915110281292">+4915110281292</a></p><br>
      <p>© 2025 · Frei teilbar.</p>
    </footer>
  </main>
</body>
</html>
